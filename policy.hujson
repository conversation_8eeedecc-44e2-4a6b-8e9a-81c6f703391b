// Combined ACL and Grants Policy File
// Merging legacy ACLs with modern grants for testing
{
	// Declare static groups of users - combined from both policies
	"groups": {
		// System and Infrastructure teams
		"group:system_admin": [
			"<EMAIL>",
			"<EMAIL>"
		],
		"group:dba": ["<EMAIL>"],
		"group:sre": ["<EMAIL>"],
		"group:all-staff": ["<EMAIL>"],
		"group:dev-team": ["<EMAIL>"],

		// Additional groups from grants policy
		"group:admin": ["<EMAIL>"],
		"group:eng": ["<EMAIL>", "<EMAIL>"],
		"group:sales": ["<EMAIL>", "<EMAIL>"],
		"group:nyc": ["<EMAIL>"],
		"group:contractors": ["<EMAIL>"],
		"group:mobile": ["<EMAIL>"],

		// grants groups
		"group:dev": ["<EMAIL>", "<EMAIL>"],
    	"group:devops": ["<EMAIL>"]
	},

	// Combined host mappings
	"hosts": {
		// Legacy hosts
		"uat1": "***************",
		"production-backend": "*************/24",
		"web-server": "***************",

		// Modern hosts
		"database": "***********",
		"api-gateway": "***********",
		"logging-server": "***********",
		"internal-tool": "***********",
		"exit-node-nyc": "************"
	},

	// Combined tag definitions
	"tagOwners": {
		"tag:dev": [],
		"tag:webserver": [],
		"tag:database": ["<EMAIL>"],
		"tag:domain-controller": ["<EMAIL>"],
		"tag:production": ["<EMAIL>"],
		"tag:linux-server": ["<EMAIL>"],
		"tag:windows-server": ["<EMAIL>"],
		"tag:security": ["<EMAIL>"],
		"tag:ci": ["<EMAIL>"],
		"tag:prod": [],

		// Modern application tags
		"tag:frontend": ["<EMAIL>", "<EMAIL>"],
		"tag:backend": ["<EMAIL>", "<EMAIL>"],
		"tag:logging": ["<EMAIL>"],
		"tag:internal-tools": ["<EMAIL>"],
		"tag:exit-node-nyc": ["<EMAIL>"],
		"tag:staging": ["<EMAIL>", "<EMAIL>"]
	},

	// Device posture definitions
	"postures": {
		"posture:latestMac": [
			"node:os == 'macos'",
			"node:osVersion >= '13.0'"
		],
		"posture:complianceDevice": [
			"node:tsVersion >= '1.50.0'",
			"node:os IN ['macos', 'windows', 'linux']"
		]
	},

	// Legacy ACL rules
	"acls": [
		// Security appliance access
		{
			"action": "accept",
			"src": ["tag:security"],
			"dst": ["*:*"]
		},

		// System admin access
		{
			"action": "accept",
			"src": ["group:system_admin"],
			"dst": ["*:*"]
		},

		// Self-device access
		{
			"action": "accept",
			"src": ["autogroup:member"],
			"dst": ["autogroup:self:*"]
		},

		// Domain controller access
		{
			"action": "accept",
			"src": ["group:all-staff"],
			"dst": ["tag:domain-controller:*"]
		},
		{
			"action": "accept",
			"src": ["tag:domain-controller"],
			"dst": ["group:all-staff:*"]
		},
		{
			"action": "accept",
			"src": ["tag:domain-controller"],
			"dst": ["tag:domain-controller:*"]
		},

		// Database access
		{
			"action": "accept",
			"src": ["group:dba", "tag:database"],
			"dst": ["tag:database:5432"]
		},

		// Development pipeline access
		{
			"action": "accept",
			"src": ["group:dev-team", "tag:ci"],
			"dst": ["uat1:22"]
		},

		// Production access
		{
			"action": "accept",
			"src": ["tag:prod"],
			"dst": ["tag:prod:*"]
		},

		// Web and database access
		{
			"action": "accept",
			"src": ["tag:webserver", "group:sre"],
			"dst": ["tag:database:*"]
		},
		{
			"action": "accept",
			"src": ["tag:webserver"],
			"dst": ["web-server:443"]
		},

		// SRE access
		{
			"action": "accept",
			"src": ["group:sre"],
			"dst": [
				"tag:database:*",
				"tag:webserver:*",
				"web-server:443",
				"production-backend:*",
				"uat1:22"
			]
		}
	],

	// Modern grants section
	"grants": [
		// Basic access grants
		{
			"src": ["*"],
			"dst": ["*"],
			"ip": ["*"]
		},
		{
			"src": ["autogroup:member"],
			"dst": ["autogroup:self"],
			"ip": ["*"]
		},
		{
			"src": ["autogroup:member"],
			"dst": ["autogroup:internet"],
			"ip": ["*"]
		},

		// Microservices communication
		{
			"src": ["tag:frontend"],
			"dst": ["tag:backend"],
			"ip": ["tcp:8080", "tcp:443"]
		},
		{
			"src": ["tag:backend"],
			"dst": ["tag:logging"],
			"ip": ["tcp:514", "udp:514"]
		},

		// Group access controls
		{
			"src": ["group:eng"],
			"dst": ["tag:internal-tools"],
			"ip": ["*"]
		},
		{
			"src": ["group:sales"],
			"dst": ["tag:internal-tools"],
			"ip": ["tcp:443", "tcp:22"]
		},

		// Location-based routing
		{
			"src": ["group:nyc"],
			"dst": ["autogroup:internet"],
			"ip": ["*"]
		},

		// Device posture controls
		{
			"src": ["group:mobile"],
			"dst": ["tag:production"],
			"srcPosture": ["posture:latestMac"],
			"ip": ["tcp:443"]
		},

		// Application specific access
		{
			"src": ["tag:frontend"],
			"dst": ["tag:backend"],
			"app": {
				"example.com/webapp-connector": [{}]
			},
			"ip": ["tcp:8080", "tcp:9090"]
		},

		// Admin compliance access
		{
			"src": ["group:admin"],
			"dst": ["tag:production"],
			"srcPosture": ["posture:complianceDevice"],
			"ip": ["*"]
		},

		// Contractor access
		{
			"src": ["group:contractors"],
			"dst": ["tag:staging"],
			"ip": ["tcp:22", "tcp:80"]
		},

		// Complex routing rules
		{
			"src": ["group:eng"],
			"dst": ["database"],
			"srcPosture": ["posture:complianceDevice"],
			"ip": ["tcp:5432", "tcp:3306"]
		},

		// Service mesh
		{
			"src": ["tag:backend"],
			"dst": ["tag:backend"],
			"ip": ["tcp:15000-15010", "tcp:9080"]
		},

		// Monitoring access
		{
			"src": ["tag:logging"],
			"dst": ["tag:frontend", "tag:backend"],
			"ip": ["udp:161", "tcp:9100", "tcp:8080"]
		},

		// Emergency access
		{
			"src": ["group:admin"],
			"dst": ["*"],
			"ip": ["tcp:22"]
		},

		// Cross-environment deployment
		{
			"src": ["tag:staging"],
			"dst": ["tag:production"],
			"srcPosture": ["posture:complianceDevice"],
			"app": {
				"example.com/deployment-pipeline": [{}]
			},
			"ip": ["tcp:443", "tcp:8443"]
		},

		// https://tailscale.com/kb/1458/grant-examples#cicd-development-pipeline
		{
			"src": ["group:dev"],
			"dst": ["tag:dev"],
			"ip": ["*"]
		},
		{
			"src": ["group:devops"],
			"dst": ["tag:ci", "tag:prod"],
			"ip": ["*"]
		},
		{
			"src": ["autogroup:member"],
			"dst": ["*********/24"],
			"via": ["tag:exit-node-nyc"],
			"ip": ["*"]
   		},
	]
}

