"""
Tailscale Network Topology Mapper - Main Application Entry Point

This module provides the main entry point for the Tailscale network topology mapping
application. It orchestrates the parsing of Tailscale policy files, building of
network graphs, and rendering of interactive HTML visualizations.

The application supports both legacy ACL rules and modern grant rules, providing
comprehensive visualization of network access relationships within a Tailscale
network infrastructure.

Example:
    Run with default settings:
        $ python main.py

    Run with debug logging:
        $ python main.py --debug

Author: Tailscale Network Topology Mapper Team
"""

import logging
import argparse

from policy_parser import PolicyParser
from network_graph import NetworkGraph
from renderer import Renderer
from enhanced_renderer import EnhancedRenderer
from live_topology import LiveTopologyBuilder
from config import LOG_FORMAT, is_tailscale_api_configured
from services import PolicyParserInterface, NetworkGraphInterface, RendererInterface, TailscaleAPIInterface
from services.container import DIContainer
from services.tailscale_api import TailscaleAPIClient


def setup_dependency_injection() -> DIContainer:
    """
    Set up dependency injection container with service registrations.

    Creates and configures a dependency injection container with all required
    service implementations, including optional Tailscale API integration.
    This promotes loose coupling and makes the application more testable
    and maintainable.

    Returns:
        DIContainer: Configured dependency injection container with all
            service implementations registered.

    Example:
        >>> container = setup_dependency_injection()
        >>> parser = container.get(PolicyParserInterface)
        >>> isinstance(parser, PolicyParser)
        True
    """
    container = DIContainer()

    # Register core service implementations
    container.register(PolicyParserInterface, PolicyParser)
    container.register(NetworkGraphInterface, NetworkGraph)
    container.register(RendererInterface, Renderer)

    # Register Tailscale API service if configured
    if is_tailscale_api_configured():
        container.register(TailscaleAPIInterface, TailscaleAPIClient)
        logging.debug("Tailscale API service registered")
    else:
        logging.debug("Tailscale API not configured, skipping service registration")

    return container


def main() -> None:
    """
    Main application entry point.

    Parses command line arguments, configures logging, sets up dependency
    injection, and orchestrates the complete network topology mapping workflow.

    The workflow consists of:
    1. Policy file parsing (JSON/HuJSON format)
    2. Network graph construction from ACL/grant rules
    3. Interactive HTML visualization rendering

    Command Line Arguments:
        --debug: Enable debug-level logging for detailed execution information

    Raises:
        SystemExit: On successful completion or unhandled errors

    Example:
        >>> main()  # Processes default policy file and generates visualization
    """
    parser = argparse.ArgumentParser(
        description="Tailscale Network Topology Mapper",
        epilog="Generates interactive HTML visualizations of Tailscale network access policies"
    )
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug logging for detailed execution information"
    )
    args = parser.parse_args()

    log_level = logging.DEBUG if args.debug else logging.INFO
    logging.basicConfig(
        level=log_level,
        format=LOG_FORMAT
    )

    logging.info("Starting Tailscale Network Topology Mapper")
    logging.debug(f"Debug logging enabled: {args.debug}")

    # Set up dependency injection
    container = setup_dependency_injection()

    try:
        logging.debug("Initializing PolicyParser via DI container")
        policy_parser = container.get(PolicyParserInterface)
        logging.debug("Parsing policy file")
        policy_parser.parse_policy()
        logging.debug(f"Policy parsing completed. Found {len(policy_parser.groups)} groups, {len(policy_parser.hosts)} hosts, {len(policy_parser.acls)} ACLs, {len(policy_parser.grants)} grants")
    except ValueError as e:
        logging.error(f"Policy parsing failed: {str(e)}")
        return

    logging.debug("Initializing NetworkGraph via DI container")
    network_graph = NetworkGraph(policy_parser.hosts, policy_parser.groups, policy_parser.rule_line_numbers)
    logging.debug("Building network graph from ACLs and grants")
    network_graph.build_graph(policy_parser.acls, policy_parser.grants)
    logging.debug(f"Network graph built with {len(network_graph.nodes)} nodes and {len(network_graph.edges)} edges")

    # Check if live topology is available and use enhanced renderer
    if is_tailscale_api_configured():
        logging.debug("Tailscale API configured, using EnhancedRenderer with live topology support")
        try:
            live_builder = LiveTopologyBuilder()
            renderer = EnhancedRenderer(network_graph, live_builder)

            # Get live topology status for logging
            live_status = renderer.get_live_status()
            logging.info(f"Live topology status: available={live_status['available']}, "
                        f"devices={live_status['device_count']}, tags={live_status['tag_count']}")

        except Exception as e:
            logging.warning(f"Failed to initialize live topology, falling back to standard renderer: {e}")
            renderer = Renderer(network_graph)
    else:
        logging.debug("Tailscale API not configured, using standard Renderer")
        renderer = Renderer(network_graph)

    logging.debug("Rendering network topology to HTML")
    renderer.render_to_html("network_topology.html")

    # Log final status
    if hasattr(renderer, 'get_live_status'):
        live_status = renderer.get_live_status()
        if live_status['available']:
            logging.info("Network topology rendered successfully with live topology integration.")
            logging.info(f"Toggle between ACL Policy View and Live Hosts View in the generated HTML.")
        else:
            logging.info("Network topology rendered successfully (policy view only).")
    else:
        logging.info("Network topology rendered successfully (policy view only).")


if __name__ == "__main__":
    main()