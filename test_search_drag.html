<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>Search Component Drag Test</title>
    <style>
        /* Include the search component styles */
        .enhanced-search-container {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background-color: #ffffff;
            padding: 15px;
            border: 2px solid #007bff;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            z-index: 1000;
            min-width: 300px;
            max-width: 400px;
            cursor: move;
            user-select: none;
        }

        .enhanced-search-container.dragging {
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
            transform: scale(1.02);
            transition: none;
            opacity: 0.9;
        }

        .enhanced-search-container .drag-handle {
            cursor: move;
            padding: 5px;
            margin: -5px -5px 5px -5px;
            border-radius: 4px 4px 0 0;
            background: linear-gradient(90deg, #007bff, #0056b3);
            color: white;
            font-weight: bold;
            text-align: center;
            font-size: 12px;
        }
    </style>
</head>

<body>
    <div class="enhanced-search-container" id="enhanced-search-container">
        <div class="drag-handle" id="drag-handle">Search (Drag to Move)</div>
        <input type="text" id="search-input" placeholder="Type to search..."
            style="width: 100%; padding: 8px; margin-top: 5px;">
    </div>

    <script>
        // Include the drag functionality
        function initializeDragFunctionality() {
            const container = document.getElementById('enhanced-search-container');
            if (!container) return;

            let isDragging = false;
            let currentX;
            let currentY;
            let initialX;
            let initialY;
            let xOffset = 0;
            let yOffset = 0;

            const dragHandle = container.querySelector('.drag-handle');
            if (!dragHandle) return;

            dragHandle.addEventListener('mousedown', dragStart);
            document.addEventListener('mousemove', drag);
            document.addEventListener('mouseup', dragEnd);

            function dragStart(e) {
                initialX = e.clientX - xOffset;
                initialY = e.clientY - yOffset;

                if (e.target === dragHandle || e.target.closest('.drag-handle')) {
                    isDragging = true;
                    container.classList.add('dragging');
                }
            }

            function drag(e) {
                if (isDragging) {
                    e.preventDefault();
                   

                    // Boundary constraints
                    const rect = container.getBoundingClientRect();
                    const maxX = window.innerWidth - rect.width;
                    const maxY = window.innerHeight - rect.height;

                    currentX = Math.max(0, Math.min(currentX, maxX));
                    currentY = Math.max(0, Math.min(currentY, maxY));

                    xOffset = currentX;
                    yOffset = currentY;

                    container.style.left = currentX + 'px';
                    container.style.top = currentY + 'px';
                    container.style.bottom = 'auto';
                }
            }

            function dragEnd() {
                if (isDragging) {
                    isDragging = false;
                    container.classList.remove('dragging');
                }
            }
        }

        // Initialize the drag functionality when the page loads
        document.addEventListener('DOMContentLoaded', function () {
            initializeDragFunctionality();
        });
    </script>
</body>

</html>