"""
Tailscale API client service for live topology integration.

This module provides a service class for interacting with the Tailscale API
to fetch live device information, tag assignments, and network status.
Supports both API key and OAuth token authentication with proper error
handling and rate limiting.

Classes:
    TailscaleAPIClient: Main API client for Tailscale API interactions
    TailscaleDevice: Data class representing a Tailscale device
    TailscaleAPIError: Custom exception for API-related errors

Features:
    - Device and tag information retrieval
    - Authentication validation
    - Rate limiting and retry logic
    - Comprehensive error handling
    - Caching for performance optimization
"""

import logging
import time
import requests
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from abc import ABC, abstractmethod

from config import TAILSCALE_API_CONFIG, get_tailscale_auth_header, is_tailscale_api_configured


class TailscaleAPIError(Exception):
    """Custom exception for Tailscale API-related errors."""
    
    def __init__(self, message: str, status_code: Optional[int] = None, response_data: Optional[Dict] = None):
        super().__init__(message)
        self.status_code = status_code
        self.response_data = response_data


@dataclass
class TailscaleDevice:
    """
    Data class representing a Tailscale device with its properties.
    
    Attributes:
        id: Unique device identifier
        hostname: Device hostname
        name: Device display name
        tags: List of assigned tags
        addresses: List of IP addresses
        online: Whether the device is currently online
        os: Operating system of the device
        client_version: Tailscale client version
        last_seen: Last seen timestamp
    """
    id: str
    hostname: str
    name: str
    tags: List[str]
    addresses: List[str]
    online: bool
    os: Optional[str] = None
    client_version: Optional[str] = None
    last_seen: Optional[str] = None


class TailscaleAPIInterface(ABC):
    """Abstract interface for Tailscale API client implementations."""
    
    @abstractmethod
    def validate_credentials(self) -> bool:
        """Validate API credentials by making a test request."""
        pass
    
    @abstractmethod
    def get_devices(self) -> List[TailscaleDevice]:
        """Fetch all devices in the tailnet."""
        pass
    
    @abstractmethod
    def get_tailnet_info(self) -> Dict[str, Any]:
        """Get basic tailnet information."""
        pass


class TailscaleAPIClient(TailscaleAPIInterface):
    """
    Tailscale API client for fetching live device and tag information.
    
    Provides methods to interact with the Tailscale API for retrieving
    device information, tag assignments, and network status. Includes
    proper authentication, error handling, and rate limiting.
    """
    
    def __init__(self, cache_duration: int = 300):
        """
        Initialize the Tailscale API client.
        
        Args:
            cache_duration: Cache duration in seconds (default: 5 minutes)
        """
        self.base_url = TAILSCALE_API_CONFIG["base_url"]
        self.tailnet = TAILSCALE_API_CONFIG["tailnet"]
        self.auth_header = get_tailscale_auth_header()
        self.cache_duration = cache_duration
        self._device_cache: Optional[List[TailscaleDevice]] = None
        self._cache_timestamp: float = 0
        self._session = requests.Session()
        
        # Set up session headers
        if self.auth_header:
            self._session.headers.update({
                "Authorization": self.auth_header,
                "Content-Type": "application/json"
            })
        
        logging.debug(f"TailscaleAPIClient initialized with base_url: {self.base_url}")
    
    def is_configured(self) -> bool:
        """
        Check if the API client is properly configured with credentials.
        
        Returns:
            True if credentials are available, False otherwise
        """
        return is_tailscale_api_configured()
    
    def validate_credentials(self) -> bool:
        """
        Validate API credentials by making a test request to the API.
        
        Returns:
            True if credentials are valid, False otherwise
            
        Raises:
            TailscaleAPIError: If there's an API communication error
        """
        if not self.is_configured():
            logging.warning("Tailscale API credentials not configured")
            return False
        
        try:
            # Make a simple request to validate credentials
            # Use the tailnet endpoint as it's lightweight and requires authentication
            endpoint = f"{self.base_url}/tailnet/{self._get_tailnet_id()}"
            response = self._make_request("GET", endpoint)
            
            if response.status_code == 200:
                logging.info("Tailscale API credentials validated successfully")
                return True
            elif response.status_code == 401:
                logging.error("Tailscale API credentials are invalid (401 Unauthorized)")
                return False
            elif response.status_code == 403:
                logging.error("Tailscale API credentials lack required permissions (403 Forbidden)")
                return False
            else:
                logging.error(f"Unexpected response from Tailscale API: {response.status_code}")
                return False
                
        except requests.RequestException as e:
            logging.error(f"Failed to validate Tailscale API credentials: {e}")
            raise TailscaleAPIError(f"API validation failed: {e}")
    
    def get_tailnet_info(self) -> Dict[str, Any]:
        """
        Get basic information about the tailnet.
        
        Returns:
            Dictionary containing tailnet information
            
        Raises:
            TailscaleAPIError: If the API request fails
        """
        if not self.is_configured():
            raise TailscaleAPIError("Tailscale API not configured")
        
        endpoint = f"{self.base_url}/tailnet/{self._get_tailnet_id()}"
        response = self._make_request("GET", endpoint)
        
        if response.status_code == 200:
            return response.json()
        else:
            raise TailscaleAPIError(
                f"Failed to get tailnet info: {response.status_code}",
                response.status_code,
                response.json() if response.content else None
            )
    
    def get_devices(self) -> List[TailscaleDevice]:
        """
        Fetch all devices in the tailnet with their tag assignments.
        
        Uses caching to avoid excessive API calls. Cache is invalidated
        after the configured cache duration.
        
        Returns:
            List of TailscaleDevice objects
            
        Raises:
            TailscaleAPIError: If the API request fails
        """
        if not self.is_configured():
            raise TailscaleAPIError("Tailscale API not configured")
        
        # Check cache first
        current_time = time.time()
        if (self._device_cache is not None and 
            current_time - self._cache_timestamp < self.cache_duration):
            logging.debug("Returning cached device data")
            return self._device_cache
        
        logging.debug("Fetching fresh device data from Tailscale API")
        endpoint = f"{self.base_url}/tailnet/{self._get_tailnet_id()}/devices"
        response = self._make_request("GET", endpoint)
        
        if response.status_code == 200:
            data = response.json()
            devices = []
            
            for device_data in data.get("devices", []):
                device = TailscaleDevice(
                    id=device_data.get("id", ""),
                    hostname=device_data.get("hostname", ""),
                    name=device_data.get("name", device_data.get("hostname", "")),
                    tags=device_data.get("tags", []),
                    addresses=device_data.get("addresses", []),
                    online=device_data.get("online", False),
                    os=device_data.get("os"),
                    client_version=device_data.get("clientVersion"),
                    last_seen=device_data.get("lastSeen")
                )
                devices.append(device)
            
            # Update cache
            self._device_cache = devices
            self._cache_timestamp = current_time
            
            logging.info(f"Fetched {len(devices)} devices from Tailscale API")
            return devices
        else:
            raise TailscaleAPIError(
                f"Failed to fetch devices: {response.status_code}",
                response.status_code,
                response.json() if response.content else None
            )
    
    def _get_tailnet_id(self) -> str:
        """
        Get the tailnet identifier for API requests.
        
        Returns:
            Tailnet identifier (configured tailnet or '-' for current user's tailnet)
        """
        return self.tailnet or "-"
    
    def _make_request(self, method: str, url: str, **kwargs) -> requests.Response:
        """
        Make an HTTP request with proper error handling and rate limiting.
        
        Args:
            method: HTTP method (GET, POST, etc.)
            url: Request URL
            **kwargs: Additional arguments for requests
            
        Returns:
            Response object
            
        Raises:
            TailscaleAPIError: If the request fails
        """
        try:
            response = self._session.request(method, url, timeout=30, **kwargs)
            
            # Handle rate limiting
            if response.status_code == 429:
                retry_after = int(response.headers.get("Retry-After", 60))
                logging.warning(f"Rate limited by Tailscale API, waiting {retry_after} seconds")
                time.sleep(retry_after)
                # Retry once after rate limit
                response = self._session.request(method, url, timeout=30, **kwargs)
            
            return response
            
        except requests.RequestException as e:
            logging.error(f"Request to {url} failed: {e}")
            raise TailscaleAPIError(f"Request failed: {e}")
    
    def clear_cache(self) -> None:
        """Clear the device cache to force fresh data on next request."""
        self._device_cache = None
        self._cache_timestamp = 0
        logging.debug("Device cache cleared")
