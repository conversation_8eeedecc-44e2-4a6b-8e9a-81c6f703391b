# Component Development Guide

## Overview

This document explains the new modular architecture for the Tailscale Network Topology Mapper's HTML generation system. The refactored architecture provides clean separation of concerns, maintainable code structure, and easy component development.

## Architecture Structure

### Directory Layout

```
templates/
├── components/          # HTML component templates
│   ├── search.html     # Enhanced search interface
│   ├── legend.html     # Legend panel with color coding
│   └── ui_fixes.html   # TomSelect integration and UI fixes
├── css/                # Stylesheet components
│   ├── base.css        # Base visualization styles
│   ├── search.css      # Search component styles
│   └── legend.css      # Legend panel styles
└── js/                 # JavaScript components
    ├── utils.js        # Shared utility functions
    ├── search.js       # Search functionality
    ├── legend.js       # Legend toggle functionality
    └── ui_fixes.js     # TomSelect integration
```

### Core Classes

#### TemplateManager (`template_manager.py`)
- **Purpose**: Handles loading and rendering of modular templates
- **Key Methods**:
  - `load_template(name)`: Load HTML template from components/
  - `load_css(name)`: Load CSS from css/
  - `load_js(name)`: Load JavaScript from js/
  - `render_template(template, variables)`: Render template with variable substitution
  - `build_search_component(metadata)`: Build complete search component
  - `build_legend_component(colors)`: Build complete legend component

#### Renderer (`renderer.py`)
- **Purpose**: Main renderer using template-based architecture
- **Key Methods**:
  - `render_to_html(output_file)`: Generate complete HTML visualization
  - `_apply_all_enhancements()`: Apply all UI enhancements in single pass
  - `_inject_search_component()`: Inject search functionality
  - `_inject_legend_component()`: Inject legend panel
  - `_inject_ui_fixes()`: Apply TomSelect and UI fixes

## Component Development

### Adding a New UI Component

1. **Create HTML Template** (`templates/components/new_component.html`):
```html
<div id="new-component-container" class="new-component">
    <h3>{{title}}</h3>
    <div class="content">
        {{content}}
    </div>
</div>
```

2. **Create CSS Styles** (`templates/css/new_component.css`):
```css
.new-component {
    position: fixed;
    top: 10px;
    right: 10px;
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
}
```

3. **Create JavaScript** (`templates/js/new_component.js`):
```javascript
function initializeNewComponent() {
    const container = document.getElementById('new-component-container');
    if (container) {
        console.log('New component initialized');
    }
}
```

4. **Add to TemplateManager**:
```python
def build_new_component(self, title, content):
    """Build new component with provided data."""
    template = self.load_template('new_component')
    css = self.load_css('new_component')
    js = self.load_js('new_component')
    
    variables = {
        'title': title,
        'content': content
    }
    
    return self.render_template(template, variables) + css + js
```

5. **Integrate in Renderer**:
```python
def _inject_new_component(self, soup):
    """Inject new component into HTML."""
    component = self.template_manager.build_new_component(
        title="My Component",
        content="Component content here"
    )
    
    # Insert before closing body tag
    body_tag = soup.find('body')
    if body_tag:
        body_tag.append(BeautifulSoup(component, 'html.parser'))
```

### Modifying Existing Components

#### Search Component
- **Template**: `templates/components/search.html`
- **Styles**: `templates/css/search.css`
- **JavaScript**: `templates/js/search.js`
- **Variables**: `{{search_metadata}}` for node data

#### Legend Component
- **Template**: `templates/components/legend.html`
- **Styles**: `templates/css/legend.css`
- **JavaScript**: `templates/js/legend.js`
- **Variables**: `{{group_color}}`, `{{tag_color}}`, `{{host_color}}`

#### UI Fixes Component
- **Template**: `templates/components/ui_fixes.html`
- **JavaScript**: `templates/js/ui_fixes.js`
- **Purpose**: TomSelect integration and bug fixes

## Best Practices

### Template Development
1. **Use semantic HTML** with proper accessibility attributes
2. **Include unique IDs** for JavaScript targeting
3. **Use CSS classes** for styling hooks
4. **Keep templates focused** on single responsibility
5. **Use variable substitution** for dynamic content

### CSS Guidelines
1. **Scope styles** to component classes
2. **Use consistent naming** (BEM methodology recommended)
3. **Include responsive breakpoints** when needed
4. **Avoid global style conflicts**
5. **Use CSS custom properties** for theming

### JavaScript Standards
1. **Wrap in initialization functions** called from main script
2. **Use defensive programming** (check element existence)
3. **Handle errors gracefully** with try-catch blocks
4. **Avoid global variable pollution**
5. **Document complex functions** with JSDoc comments

### Variable Substitution
- Use `{{variable_name}}` syntax in templates
- Escape HTML content when needed
- Provide default values for optional variables
- Validate variable types in TemplateManager

## Testing Components

### Unit Testing
```python
def test_new_component_generation():
    template_manager = TemplateManager()
    component = template_manager.build_new_component("Test", "Content")
    
    assert "Test" in component
    assert "Content" in component
    assert "new-component-container" in component
```

### Integration Testing
```python
def test_component_in_full_render():
    renderer = Renderer(network_graph)
    renderer.render_to_html("test_output.html")
    
    with open("test_output.html", 'r') as f:
        content = f.read()
    
    assert "new-component-container" in content
```

## Migration Guide

### From Monolithic to Modular

1. **Identify component boundaries** in existing code
2. **Extract HTML templates** to separate files
3. **Move CSS styles** to component-specific files
4. **Separate JavaScript functions** by functionality
5. **Update renderer** to use template system
6. **Test thoroughly** with existing data

### Backward Compatibility
- All existing functionality is preserved
- Same HTML output structure maintained
- No changes to external APIs
- Command-line options unchanged

## Troubleshooting

### Common Issues

1. **Template not found**: Check file path and naming
2. **Variable not substituted**: Verify variable name and data type
3. **CSS not applied**: Check CSS selector specificity
4. **JavaScript errors**: Verify element IDs and initialization order
5. **Component not visible**: Check CSS positioning and z-index

### Debugging Tips
- Use browser developer tools for CSS/JS debugging
- Check console for JavaScript errors
- Validate HTML structure with browser inspector
- Test components in isolation before integration
- Use logging in TemplateManager for template loading issues

## Performance Considerations

- **Template caching**: Templates are loaded once and reused
- **Single-pass rendering**: All enhancements applied in one operation
- **Minimal DOM manipulation**: Use BeautifulSoup for efficient parsing
- **CSS optimization**: Combine related styles in single files
- **JavaScript efficiency**: Initialize components only when needed

## Future Enhancements

- **Template inheritance**: Support for base templates and extends
- **Component composition**: Nested component support
- **Theme system**: CSS custom properties for easy theming
- **Hot reloading**: Development mode with automatic template refresh
- **Component library**: Reusable UI components across projects
