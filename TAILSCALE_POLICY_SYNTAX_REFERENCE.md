# Tailscale Policy Syntax Reference

This document provides correct syntax patterns for Tailscale policy files based on official documentation.

## File Format
- Use HuJSON (Human JSON) format with `.hujson` extension
- Comments allowed with `//` syntax
- Trailing commas permitted

## Basic Structure
```json
{
  "groups": {
    "group:name": ["<EMAIL>", "tag:server"]
  },
  "hosts": {
    "host-alias": "***********"
  },
  "tagOwners": {
    "tag:server": ["group:admins"],
    "tag:dev": ["autogroup:admin"]
  },
  "postures": {
    "posture:secure-mac": [
      "node:os == 'macos'",
      "node:tsVersion >= '1.40'"
    ]
  },
  "acls": [
    {
      "action": "accept",
      "src": ["group:users"],
      "dst": ["tag:server:80,443"]
    }
  ],
  "grants": [
    {
      "src": ["group:users"],
      "dst": ["tag:server"],
      "ip": ["80", "443"],
      "app": {
        "example.com/webapp": [{}]
      }
    }
  ]
}
```

## Groups
- **Format**: `"group:name"` (no spaces in group names)
- **Correct**: `"group:all-staff"`, `"group:dev_team"`
- **Incorrect**: `"group:all staff"` (contains space)

## Tags
- **Format**: `"tag:name"`
- **Must be defined in tagOwners**: All referenced tags must exist in tagOwners section
- **Examples**: `"tag:server"`, `"tag:dev"`, `"tag:production"`

## Device Posture
### Posture Definitions
```json
"postures": {
  "posture:secure-devices": [
    "node:os IN ['macos', 'windows', 'linux']",
    "node:tsVersion >= '1.40'",
    "node:tsReleaseTrack == 'stable'"
  ]
}
```

### Valid OS Names
- `macos` (lowercase)
- `windows` (lowercase)
- `linux` (lowercase)
- `ios`, `android`, `freebsd`, `openbsd`, `illumos`, `js`

### Valid Operators
- `==`, `!=` (equality)
- `IN`, `NOT IN` (membership)
- `<`, `<=`, `>=`, `>` (version/numeric comparison)

### Valid Attributes
- `node:os` - Operating system
- `node:osVersion` - OS version string
- `node:tsVersion` - Tailscale version
- `node:tsReleaseTrack` - Release track ('stable', 'unstable')
- `node:tsAutoUpdate` - Auto-update enabled (true/false)
- `ip:country` - Country code (Enterprise only)

## ACLs (Legacy)
```json
"acls": [
  {
    "action": "accept",
    "src": ["group:users"],
    "dst": ["tag:server:80,443", "tag:database:5432"]
  }
]
```
- **Destination format**: Must include ports (`tag:server:80,443`)
- **Actions**: `accept` (default if omitted)

## Grants (Modern)
### Basic Grant Structure
```json
"grants": [
  {
    "src": ["group:users"],
    "dst": ["tag:server"],
    "ip": ["80", "443", "tcp:22"],
    "srcPosture": ["posture:secure-devices"],
    "via": ["tag:exit-node"]
  }
]
```

### Network Layer Capabilities (`ip` field)
- `"*"` - All ports/protocols
- `"80"` - Specific port (implies TCP/UDP/ICMP)
- `"80-443"` - Port range
- `"tcp:80"` - Protocol:port combination
- `"icmp:*"` - All ICMP traffic

### Application Layer Capabilities (`app` field)
```json
"app": {
  "domain.com/capability": [
    {
      "parameter": "value"
    }
  ]
}
```
- **Format**: `"domain/capability"` (not array)
- **Correct**: `"example.com/webapp": [{}]`
- **Incorrect**: `"app": ["example.com/webapp"]`

### Common App Capabilities
```json
// TailSQL access
"tailscale.com/cap/tailsql": [
  {"dataSrc": ["*"]}
]

// Golink admin
"tailscale.com/cap/golink": [
  {"admin": true}
]

// Kubernetes impersonation
"tailscale.com/cap/kubernetes": [
  {
    "impersonate": {
      "groups": ["system:masters"]
    }
  }
]
```

## Via Routing
- **Purpose**: Specify routing through exit nodes, subnet routers, app connectors
- **Format**: Array of tags identifying routing devices
- **Example**: `"via": ["tag:exit-node", "tag:subnet-router"]`
- **Restrictions**: Cannot use with certain destination types

## Selectors
### Source/Destination Selectors
- `"*"` - All devices in tailnet
- `"<EMAIL>"` - Specific user
- `"username@github"` - GitHub user
- `"username@passkey"` - Passkey user
- `"group:name"` - Group members
- `"tag:name"` - Tagged devices
- `"autogroup:admin"` - Role-based autogroup
- `"autogroup:member"` - All tailnet members
- `"autogroup:tagged"` - All tagged devices
- `"autogroup:internet"` - Internet via exit nodes (dst only)
- `"autogroup:self"` - User's own devices (dst only)
- `"***********/24"` - CIDR range
- `"host-alias"` - Host alias from hosts section
- `"ipset:name"` - IP set reference

## Common Validation Errors and Fixes

### 1. App Field Format
**Error**: Array format for app capabilities
```json
// WRONG
"app": ["example.com/webapp"]

// CORRECT
"app": {
  "example.com/webapp": [{}]
}
```

### 2. Group Names with Spaces
**Error**: Spaces in group names
```json
// WRONG
"group:all staff"

// CORRECT
"group:all-staff"
```

### 3. Missing Tag Definitions
**Error**: Referenced tags not in tagOwners
```json
// WRONG - tag:dev used but not defined

// CORRECT
"tagOwners": {
  "tag:dev": ["autogroup:admin"]
}
```

### 4. ACL Destination Format
**Error**: Missing port specifications
```json
// WRONG
"dst": ["tag:server"]

// CORRECT
"dst": ["tag:server:80,443"]
```

### 5. Device Posture OS Names
**Error**: Incorrect case for OS names
```json
// WRONG
"node:os == 'MacOS'"

// CORRECT
"node:os == 'macos'"
```

### 6. Via Field Restrictions
**Error**: Using via with incompatible destinations
```json
// WRONG - via cannot be used with certain destination types
"dst": ["autogroup:internet"],
"via": ["tag:exit-node"]

// CORRECT - remove via when using autogroup:internet
"dst": ["autogroup:internet"]
```

## Validation Commands
```bash
# Set environment variables
export TAILSCALE_TAILNET="your-tailnet"
export TAILSCALE_API_KEY="your-api-key"

# Validate policy file
./scripts/validate-policy.sh policy.hujson
```

## Success Indicators
- Empty JSON response `{}` from validation API
- Message: `✔ Tailscale policy is valid.`
