"""
Enhanced renderer with support for both ACL Policy View and Live Hosts View.

This module extends the base Renderer class to support dual-mode visualization:
- ACL Policy View: Shows policy-defined access rules (existing functionality)
- Live Hosts View: Shows actual device-tag relationships from Tailscale API

The enhanced renderer can generate HTML files that support switching between
both views dynamically, providing users with comprehensive network visibility.

Classes:
    EnhancedRenderer: Extended renderer with live topology support
"""

import logging
import json
import os
from typing import Optional, Dict, Any

from renderer import Renderer
from network_graph import NetworkGraph
from live_topology import LiveTopologyBuilder, LiveNetworkGraph
from services.tailscale_api import TailscaleAPIError
from config import is_tailscale_api_configured


class EnhancedRenderer(Renderer):
    """
    Enhanced renderer supporting both policy and live topology views.
    
    Extends the base Renderer class to support dual-mode visualization
    with dynamic switching between ACL policy rules and live device data.
    """
    
    def __init__(self, network_graph: NetworkGraph, live_builder: Optional[LiveTopologyBuilder] = None):
        """
        Initialize the enhanced renderer.
        
        Args:
            network_graph: NetworkGraph instance for policy-based topology
            live_builder: Optional LiveTopologyBuilder for live topology data
        """
        super().__init__(network_graph)
        self.live_builder = live_builder or LiveTopologyBuilder()
        self.live_graph: Optional[LiveNetworkGraph] = None
        self.live_available = False
        
        # Check if live topology is available
        if is_tailscale_api_configured():
            try:
                self.live_available = self.live_builder.is_available()
                if self.live_available:
                    logging.info("Live topology integration available")
                else:
                    logging.warning("Live topology configured but not accessible")
            except Exception as e:
                logging.error(f"Failed to check live topology availability: {e}")
                self.live_available = False
        else:
            logging.debug("Live topology not configured")
    
    def render_to_html(self, output_file: str) -> None:
        """
        Render the enhanced network visualization to HTML.
        
        Creates an HTML file with both policy and live topology data,
        allowing users to switch between views dynamically.
        
        Args:
            output_file: Path where the HTML file should be written
        """
        logging.debug(f"Starting enhanced HTML rendering to: {output_file}")
        
        # First, render the base policy view
        super().render_to_html(output_file)
        
        # If live topology is available, prepare live data and enhance the HTML
        if self.live_available:
            try:
                logging.debug("Preparing live topology data")
                self.live_graph = self.live_builder.build_live_topology()
                
                if self.live_graph:
                    self._add_live_topology_data()
                    logging.info("Enhanced rendering completed with live topology support")
                else:
                    logging.warning("Live topology data not available")
            except TailscaleAPIError as e:
                logging.error(f"Failed to fetch live topology data: {e}")
                self._add_error_message(str(e))
        else:
            logging.debug("Live topology not available, using policy-only rendering")
    
    def _add_live_topology_data(self) -> None:
        """
        Add live topology data to the HTML file for dynamic view switching.
        
        Embeds the live topology graph data as JavaScript variables that can
        be used by the view toggle functionality.
        """
        if not self.live_graph:
            logging.warning("No live graph data available")
            return
        
        logging.debug("Adding live topology data to HTML")
        
        # Prepare live topology data in vis.js format
        live_nodes = []
        live_edges = []
        
        for node, color, tooltip_text, shape in self.live_graph.nodes:
            live_nodes.append({
                'id': node,
                'label': node,
                'color': color,
                'title': tooltip_text,
                'shape': shape
            })
        
        for src, dst in self.live_graph.edges:
            live_edges.append({
                'from': src,
                'to': dst,
                'arrows': {'to': {'enabled': True}}
            })
        
        # Create JavaScript to embed the live data
        live_data_script = f"""
<script>
// Live topology data for view switching
const liveTopologyData = {{
    nodes: {json.dumps(live_nodes)},
    edges: {json.dumps(live_edges)}
}};

// Enhanced view switching with live data
function updateNetworkWithLiveData(liveData) {{
    if (typeof network !== 'undefined' && liveData) {{
        nodes.clear();
        edges.clear();
        nodes.add(liveData.nodes);
        edges.add(liveData.edges);
        network.fit();
        
        // Update search metadata if available
        if (typeof searchMetadata !== 'undefined') {{
            // Update search metadata with live data
            updateSearchMetadataForLiveView();
        }}
    }}
}}

function updateSearchMetadataForLiveView() {{
    // Update search metadata to work with live topology
    // This would need to be implemented based on the live graph's metadata
    console.log('Updating search metadata for live view');
}}

// Override the fetchLiveTopology function to use embedded data
function fetchLiveTopology() {{
    const loadingDiv = document.getElementById('loading-indicator');
    const statusDiv = document.getElementById('view-status');
    
    if (loadingDiv) {{
        loadingDiv.style.display = 'block';
    }}
    
    // Simulate brief loading time
    setTimeout(function() {{
        if (loadingDiv) {{
            loadingDiv.style.display = 'none';
        }}
        
        if (statusDiv) {{
            statusDiv.style.background = '#cce5ff';
            statusDiv.style.color = '#004085';
            statusDiv.innerHTML = '🌐 Showing live device-tag relationships';
        }}
        
        // Use the embedded live data
        updateNetworkWithLiveData(liveTopologyData);
        
        // Cache the live data
        window.liveTopologyData = liveTopologyData;
        
    }}, 500);
}}

// Add device count information
document.addEventListener('DOMContentLoaded', function() {{
    const deviceCount = liveTopologyData.nodes.filter(n => 
        n.title && n.title.includes('Source: Live Tailscale API') && 
        !n.id.startsWith('tag:')
    ).length;
    
    const tagCount = liveTopologyData.nodes.filter(n => 
        n.id.startsWith('tag:') || n.shape === 'triangle'
    ).length;
    
    console.log(`Live topology loaded: ${{deviceCount}} devices, ${{tagCount}} tags`);
}});
</script>
"""
        
        # Insert the live data script into the HTML
        with open(self.output_file, "r") as f:
            content = f.read()
        
        # Insert before the closing body tag
        content = content.replace("</body>", f"{live_data_script}</body>")
        
        with open(self.output_file, "w") as f:
            f.write(content)
        
        logging.debug("Live topology data embedded successfully")
    
    def _add_error_message(self, error_msg: str) -> None:
        """
        Add an error message to the HTML when live topology fails.
        
        Args:
            error_msg: Error message to display
        """
        error_script = f"""
<script>
// Override fetchLiveTopology to show error
function fetchLiveTopology() {{
    const loadingDiv = document.getElementById('loading-indicator');
    const statusDiv = document.getElementById('view-status');
    
    if (loadingDiv) {{
        loadingDiv.style.display = 'none';
    }}
    
    if (statusDiv) {{
        statusDiv.style.background = '#f8d7da';
        statusDiv.style.color = '#721c24';
        statusDiv.innerHTML = '❌ Live topology error: {error_msg}';
    }}
    
    // Switch back to policy view
    const policyRadio = document.getElementById('policy-view-radio');
    if (policyRadio) {{
        policyRadio.checked = true;
        switchToView('policy');
    }}
}}
</script>
"""
        
        # Insert the error script into the HTML
        with open(self.output_file, "r") as f:
            content = f.read()
        
        content = content.replace("</body>", f"{error_script}</body>")
        
        with open(self.output_file, "w") as f:
            f.write(content)
        
        logging.debug("Error message added to HTML")
    
    def get_live_status(self) -> Dict[str, Any]:
        """
        Get the current status of live topology integration.
        
        Returns:
            Dictionary with live topology status information
        """
        status = {
            "available": self.live_available,
            "configured": is_tailscale_api_configured(),
            "graph_built": self.live_graph is not None,
            "device_count": 0,
            "tag_count": 0
        }
        
        if self.live_graph:
            # Count devices and tags in live graph
            devices = [node for node, _, tooltip, _ in self.live_graph.nodes 
                      if not node.startswith('tag:')]
            tags = [node for node, _, tooltip, _ in self.live_graph.nodes 
                   if node.startswith('tag:')]
            
            status["device_count"] = len(devices)
            status["tag_count"] = len(tags)
        
        return status
