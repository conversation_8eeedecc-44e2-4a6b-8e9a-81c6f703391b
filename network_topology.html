<html>
    <head>
        <meta charset="utf-8">
        
            <script>function neighbourhoodHighlight(params) {
  // console.log("in nieghbourhoodhighlight");
  allNodes = nodes.get({ returnType: "Object" });
  // originalNodes = JSON.parse(JSON.stringify(allNodes));
  // if something is selected:
  if (params.nodes.length > 0) {
    highlightActive = true;
    var i, j;
    var selectedNode = params.nodes[0];
    var degrees = 2;

    // mark all nodes as hard to read.
    for (let nodeId in allNodes) {
      // nodeColors[nodeId] = allNodes[nodeId].color;
      allNodes[nodeId].color = "rgba(200,200,200,0.5)";
      if (allNodes[nodeId].hiddenLabel === undefined) {
        allNodes[nodeId].hiddenLabel = allNodes[nodeId].label;
        allNodes[nodeId].label = undefined;
      }
    }
    var connectedNodes = network.getConnectedNodes(selectedNode);
    var allConnectedNodes = [];

    // get the second degree nodes
    for (i = 1; i < degrees; i++) {
      for (j = 0; j < connectedNodes.length; j++) {
        allConnectedNodes = allConnectedNodes.concat(
          network.getConnectedNodes(connectedNodes[j])
        );
      }
    }

    // all second degree nodes get a different color and their label back
    for (i = 0; i < allConnectedNodes.length; i++) {
      // allNodes[allConnectedNodes[i]].color = "pink";
      allNodes[allConnectedNodes[i]].color = "rgba(150,150,150,0.75)";
      if (allNodes[allConnectedNodes[i]].hiddenLabel !== undefined) {
        allNodes[allConnectedNodes[i]].label =
          allNodes[allConnectedNodes[i]].hiddenLabel;
        allNodes[allConnectedNodes[i]].hiddenLabel = undefined;
      }
    }

    // all first degree nodes get their own color and their label back
    for (i = 0; i < connectedNodes.length; i++) {
      // allNodes[connectedNodes[i]].color = undefined;
      allNodes[connectedNodes[i]].color = nodeColors[connectedNodes[i]];
      if (allNodes[connectedNodes[i]].hiddenLabel !== undefined) {
        allNodes[connectedNodes[i]].label =
          allNodes[connectedNodes[i]].hiddenLabel;
        allNodes[connectedNodes[i]].hiddenLabel = undefined;
      }
    }

    // the main node gets its own color and its label back.
    // allNodes[selectedNode].color = undefined;
    allNodes[selectedNode].color = nodeColors[selectedNode];
    if (allNodes[selectedNode].hiddenLabel !== undefined) {
      allNodes[selectedNode].label = allNodes[selectedNode].hiddenLabel;
      allNodes[selectedNode].hiddenLabel = undefined;
    }
  } else if (highlightActive === true) {
    // console.log("highlightActive was true");
    // reset all nodes
    for (let nodeId in allNodes) {
      // allNodes[nodeId].color = "purple";
      allNodes[nodeId].color = nodeColors[nodeId];
      // delete allNodes[nodeId].color;
      if (allNodes[nodeId].hiddenLabel !== undefined) {
        allNodes[nodeId].label = allNodes[nodeId].hiddenLabel;
        allNodes[nodeId].hiddenLabel = undefined;
      }
    }
    highlightActive = false;
  }

  // transform the object into an array
  var updateArray = [];
  if (params.nodes.length > 0) {
    for (let nodeId in allNodes) {
      if (allNodes.hasOwnProperty(nodeId)) {
        // console.log(allNodes[nodeId]);
        updateArray.push(allNodes[nodeId]);
      }
    }
    nodes.update(updateArray);
  } else {
    // console.log("Nothing was selected");
    for (let nodeId in allNodes) {
      if (allNodes.hasOwnProperty(nodeId)) {
        // console.log(allNodes[nodeId]);
        // allNodes[nodeId].color = {};
        updateArray.push(allNodes[nodeId]);
      }
    }
    nodes.update(updateArray);
  }
}

function filterHighlight(params) {
  allNodes = nodes.get({ returnType: "Object" });
  // if something is selected:
  if (params.nodes.length > 0) {
    filterActive = true;
    let selectedNodes = params.nodes;

    // hiding all nodes and saving the label
    for (let nodeId in allNodes) {
      allNodes[nodeId].hidden = true;
      if (allNodes[nodeId].savedLabel === undefined) {
        allNodes[nodeId].savedLabel = allNodes[nodeId].label;
        allNodes[nodeId].label = undefined;
      }
    }

    for (let i=0; i < selectedNodes.length; i++) {
      allNodes[selectedNodes[i]].hidden = false;
      if (allNodes[selectedNodes[i]].savedLabel !== undefined) {
        allNodes[selectedNodes[i]].label = allNodes[selectedNodes[i]].savedLabel;
        allNodes[selectedNodes[i]].savedLabel = undefined;
      }
    }

  } else if (filterActive === true) {
    // reset all nodes
    for (let nodeId in allNodes) {
      allNodes[nodeId].hidden = false;
      if (allNodes[nodeId].savedLabel !== undefined) {
        allNodes[nodeId].label = allNodes[nodeId].savedLabel;
        allNodes[nodeId].savedLabel = undefined;
      }
    }
    filterActive = false;
  }

  // transform the object into an array
  var updateArray = [];
  if (params.nodes.length > 0) {
    for (let nodeId in allNodes) {
      if (allNodes.hasOwnProperty(nodeId)) {
        updateArray.push(allNodes[nodeId]);
      }
    }
    nodes.update(updateArray);
  } else {
    for (let nodeId in allNodes) {
      if (allNodes.hasOwnProperty(nodeId)) {
        updateArray.push(allNodes[nodeId]);
      }
    }
    nodes.update(updateArray);
  }
}

function selectNode(nodes) {
  network.selectNodes(nodes);
  neighbourhoodHighlight({ nodes: nodes });
  return nodes;
}

function selectNodes(nodes) {
  network.selectNodes(nodes);
  filterHighlight({nodes: nodes});
  return nodes;
}

function highlightFilter(filter) {
  let selectedNodes = []
  let selectedProp = filter['property']
  if (filter['item'] === 'node') {
    let allNodes = nodes.get({ returnType: "Object" });
    for (let nodeId in allNodes) {
      if (allNodes[nodeId][selectedProp] && filter['value'].includes((allNodes[nodeId][selectedProp]).toString())) {
        selectedNodes.push(nodeId)
      }
    }
  }
  else if (filter['item'] === 'edge'){
    let allEdges = edges.get({returnType: 'object'});
    // check if the selected property exists for selected edge and select the nodes connected to the edge
    for (let edge in allEdges) {
      if (allEdges[edge][selectedProp] && filter['value'].includes((allEdges[edge][selectedProp]).toString())) {
        selectedNodes.push(allEdges[edge]['from'])
        selectedNodes.push(allEdges[edge]['to'])
      }
    }
  }
  selectNodes(selectedNodes)
}</script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.2/dist/dist/vis-network.min.css" integrity="sha512-WgxfT5LWjfszlPHXRmBWHkV2eceiWTOBvrKCNbdgDYTHrT2AeLCGbF4sZlZw3UMN3WtL0tGUoIAKsu8mllg/XA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
            <script src="https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.2/dist/vis-network.min.js" integrity="sha512-LnvoEWDFrqGHlHmDD2101OrLcbsfkrzoSpvtSQtxK3RMnRV0eOkhhBN2dXHKRrUU8p2DGRTk35n4O8nWSVe1mQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
            
            
            
            
            
            
                <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/tom-select/2.0.0-rc.4/css/tom-select.min.css" integrity="sha512-43fHB3GLgZfz8QXl1RPQ8O66oIgv3po9cJ5erMt1c4QISq9dYb195T3vr5ImnJPXuVroKcGBPXBFKETW8jrPNQ==" crossorigin="anonymous" referrerpolicy="no-referrer" />
                <script src="https://cdnjs.cloudflare.com/ajax/libs/tom-select/2.0.0-rc.4/js/tom-select.complete.js" integrity="sha512-jeF9CfnvzDiw9G9xiksVjxR2lib44Gnovvkv+3CgCG6NXCD4gqlA5nDAVW5WjpA+i+/zKsUWV5xNEbW1X/HH0Q==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
            

        
<center>
<h1></h1>
</center>

<!-- <link rel="stylesheet" href="../node_modules/vis/dist/vis.min.css" type="text/css" />
<script type="text/javascript" src="../node_modules/vis/dist/vis.js"> </script>-->
        <link
          href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta3/dist/css/bootstrap.min.css"
          rel="stylesheet"
          integrity="sha384-eOJMYsd53ii+scO/bJGFsiCZc+5NDVN2yr8+0RDqr0Ql0h+rP48ckxlpbzKgwra6"
          crossorigin="anonymous"
        />
        <script
          src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta3/dist/js/bootstrap.bundle.min.js"
          integrity="sha384-JEW9xMcG8R+pH31jmWH6WWP0WintQrMb4s7ZOdauHnUtxwoG2vI5DkLtS3qm9Ekf"
          crossorigin="anonymous"
        ></script>


        <center>
          <h1></h1>
        </center>
        <style type="text/css">

             #mynetwork {
                 width: 100%;
                 height: 800px;
                 background-color: #ffffff;
                 border: 1px solid lightgray;
                 position: relative;
                 float: left;
             }

             

             

             
        </style>
    </head>


    <body>
        <div class="card" style="width: 100%">
            
                <div id="select-menu" class="card-header">
                    <div class="row no-gutters">
                        <div class="col-8 pb-2">
                            <select
                            class="form-select"
                            aria-label="Default select example"
                            onchange="selectNode([value]);"
                            id="select-node"
                            placeholder="Select node..."
                            >
                                <option value="" disabled selected>Select a Node by ID</option>
                                
                                    <option value="tag:logging [tcp:514, udp:514]">tag:logging [tcp:514, udp:514]</option>
                                
                                    <option value="uat1:22">uat1:22</option>
                                
                                    <option value="group:all-staff">group:all-staff</option>
                                
                                    <option value="tag:staging [tcp:22,80]">tag:staging [tcp:22,80]</option>
                                
                                    <option value="tag:prod:*">tag:prod:*</option>
                                
                                    <option value="*********/24">*********/24</option>
                                
                                    <option value="group:sales">group:sales</option>
                                
                                    <option value="group:admin">group:admin</option>
                                
                                    <option value="group:devops">group:devops</option>
                                
                                    <option value="tag:webserver">tag:webserver</option>
                                
                                    <option value="tag:domain-controller:*">tag:domain-controller:*</option>
                                
                                    <option value="tag:staging">tag:staging</option>
                                
                                    <option value="tag:domain-controller">tag:domain-controller</option>
                                
                                    <option value="production-backend:*">production-backend:*</option>
                                
                                    <option value="web-server:443">web-server:443</option>
                                
                                    <option value="tag:database:*">tag:database:*</option>
                                
                                    <option value="autogroup:self:*">autogroup:self:*</option>
                                
                                    <option value="tag:logging">tag:logging</option>
                                
                                    <option value="tag:security">tag:security</option>
                                
                                    <option value="tag:backend [tcp:15000-15010,9080]">tag:backend [tcp:15000-15010,9080]</option>
                                
                                    <option value="autogroup:internet">autogroup:internet</option>
                                
                                    <option value="group:system_admin">group:system_admin</option>
                                
                                    <option value="*">*</option>
                                
                                    <option value="group:dba">group:dba</option>
                                
                                    <option value="tag:backend [udp:161, tcp:9100,8080]">tag:backend [udp:161, tcp:9100,8080]</option>
                                
                                    <option value="tag:internal-tools">tag:internal-tools</option>
                                
                                    <option value="autogroup:self">autogroup:self</option>
                                
                                    <option value="tag:backend [tcp:8080,443]">tag:backend [tcp:8080,443]</option>
                                
                                    <option value="autogroup:member">autogroup:member</option>
                                
                                    <option value="tag:frontend [udp:161, tcp:9100,8080]">tag:frontend [udp:161, tcp:9100,8080]</option>
                                
                                    <option value="tag:backend">tag:backend</option>
                                
                                    <option value="group:dev">group:dev</option>
                                
                                    <option value="tag:internal-tools [tcp:443,22]">tag:internal-tools [tcp:443,22]</option>
                                
                                    <option value="group:contractors">group:contractors</option>
                                
                                    <option value="tag:frontend">tag:frontend</option>
                                
                                    <option value="tag:dev">tag:dev</option>
                                
                                    <option value="tag:production [tcp:443,8443]">tag:production [tcp:443,8443]</option>
                                
                                    <option value="tag:backend [tcp:8080,9090]">tag:backend [tcp:8080,9090]</option>
                                
                                    <option value="group:all-staff:*">group:all-staff:*</option>
                                
                                    <option value="group:dev-team">group:dev-team</option>
                                
                                    <option value="group:mobile">group:mobile</option>
                                
                                    <option value="database [tcp:5432,3306]">database [tcp:5432,3306]</option>
                                
                                    <option value="tag:production [tcp:443]">tag:production [tcp:443]</option>
                                
                                    <option value="tag:ci">tag:ci</option>
                                
                                    <option value="group:eng">group:eng</option>
                                
                                    <option value="* [tcp:22]">* [tcp:22]</option>
                                
                                    <option value="tag:database:5432">tag:database:5432</option>
                                
                                    <option value="*:*">*:*</option>
                                
                                    <option value="tag:prod">tag:prod</option>
                                
                                    <option value="group:nyc">group:nyc</option>
                                
                                    <option value="tag:webserver:*">tag:webserver:*</option>
                                
                                    <option value="tag:production">tag:production</option>
                                
                                    <option value="tag:database">tag:database</option>
                                
                                    <option value="group:sre">group:sre</option>
                                
                            </select>
                        </div>
                        <div class="col-2 pb-2">
                            <button type="button" class="btn btn-primary btn-block" onclick="resetSelection();">Reset Selection</button>
                        </div>
                        <div class="col-2 pb-2">
                            <button type="button" class="btn btn-success btn-block" id="legend-toggle-inline" onclick="toggleLegend();">📊 Legend</button>
                        </div>
                    </div>
                </div>
            
            
            <div id="mynetwork" class="card-body"></div>
        </div>

        
        

        <script type="text/javascript">

              // initialize global variables.
              var edges;
              var nodes;
              var allNodes;
              var allEdges;
              var nodeColors;
              var originalNodes;
              var network;
              var container;
              var options, data;
              var filter = {
                  item : '',
                  property : '',
                  value : []
              };

              
                  new TomSelect("#select-node",{
                      create: false,
                      sortField: {
                          field: "text",
                          direction: "asc"
                      }
                  });
              

              

              // This method is responsible for drawing the graph, returns the drawn network
              function drawGraph() {
                  var container = document.getElementById('mynetwork');

                  

                  // parsing and collecting nodes and edges from the python
                  nodes = new vis.DataSet([{"color": "#00cc66", "id": "tag:logging [tcp:514, udp:514]", "label": "tag:logging [tcp:514, udp:514]", "shape": "triangle", "title": "tag:logging [tcp:514, udp:514]"}, {"color": "#ff6666", "id": "uat1:22", "label": "uat1:22", "shape": "dot", "title": "uat1:22\n\u2139\ufe0f  100.101.102.103\n\ud83d\udd12 Legacy ACL Rules\n\ud83d\udccb Rule References:\n  \u2022 Destination: ACL rule 8 (Ln 126), ACL rule 12 (Ln 152)"}, {"color": "#FFFF00", "id": "group:all-staff", "label": "group:all-staff", "shape": "dot", "title": "group:all-staff\n\ud83d\udd12 Legacy ACL Rules\n\ud83d\udccb Rule References:\n  \u2022 Source: ACL rule 4 (Ln 102)\n\ud83d\udc65 Group Members: <EMAIL>"}, {"color": "#00cc66", "id": "tag:staging [tcp:22,80]", "label": "tag:staging [tcp:22,80]", "shape": "triangle", "title": "tag:staging [tcp:22,80]"}, {"color": "#00cc66", "id": "tag:prod:*", "label": "tag:prod:*", "shape": "dot", "title": "tag:prod:*\n\ud83d\udd12 Legacy ACL Rules\n\ud83d\udccb Rule References:\n  \u2022 Destination: ACL rule 9 (Ln 133)"}, {"color": "#ff6666", "id": "*********/24", "label": "*********/24", "shape": "triangle", "title": "*********/24\n\ud83c\udfaf Modern Grant Rules\n\ud83d\udccb Rule References:\n  \u2022 Destination: Grant rule 20 (Ln 299)\n\ud83d\udee4\ufe0f  Via Routes: tag:exit-node-nyc"}, {"color": "#FFFF00", "id": "group:sales", "label": "group:sales", "shape": "triangle", "title": "group:sales\n\ud83c\udfaf Modern Grant Rules\n\ud83d\udccb Rule References:\n  \u2022 Source: Grant rule 7 (Ln 202)\n\ud83c\udf10 Protocols: tcp:443, tcp:22\n\ud83d\udc65 Group Members: <EMAIL>, <EMAIL>"}, {"color": "#FFFF00", "id": "group:admin", "label": "group:admin", "shape": "triangle", "title": "group:admin\n\ud83c\udfaf Modern Grant Rules\n\ud83d\udccb Rule References:\n  \u2022 Source: Grant rule 11 (Ln 234), Grant rule 16 (Ln 271)\n\ud83c\udf10 Protocols: *, tcp:22\n\ud83d\udd10 Posture Checks: posture:complianceDevice\n\ud83d\udc65 Group Members: <EMAIL>"}, {"color": "#FFFF00", "id": "group:devops", "label": "group:devops", "shape": "triangle", "title": "group:devops\n\ud83c\udfaf Modern Grant Rules\n\ud83d\udccb Rule References:\n  \u2022 Source: Grant rule 19 (Ln 294)\n\ud83d\udc65 Group Members: <EMAIL>"}, {"color": "#00cc66", "id": "tag:webserver", "label": "tag:webserver", "shape": "dot", "title": "tag:webserver\n\ud83d\udd12 Legacy ACL Rules\n\ud83d\udccb Rule References:\n  \u2022 Source: ACL rule 10 (Ln 140), ACL rule 11 (Ln 145)"}, {"color": "#00cc66", "id": "tag:domain-controller:*", "label": "tag:domain-controller:*", "shape": "dot", "title": "tag:domain-controller:*\n\ud83d\udd12 Legacy ACL Rules\n\ud83d\udccb Rule References:\n  \u2022 Destination: ACL rule 4 (Ln 102), ACL rule 6 (Ln 112)"}, {"color": "#00cc66", "id": "tag:staging", "label": "tag:staging", "shape": "triangle", "title": "tag:staging\n\ud83c\udfaf Modern Grant Rules\n\ud83d\udccb Rule References:\n  \u2022 Source: Grant rule 17 (Ln 278)\n  \u2022 Destination: Grant rule 12 (Ln 242)\n\ud83c\udf10 Protocols: tcp:80, tcp:443, tcp:22, tcp:8443\n\ud83d\udd10 Posture Checks: posture:complianceDevice\n\ud83d\udcf1 Applications: example.com/deployment-pipeline"}, {"color": "#00cc66", "id": "tag:domain-controller", "label": "tag:domain-controller", "shape": "dot", "title": "tag:domain-controller\n\ud83d\udd12 Legacy ACL Rules\n\ud83d\udccb Rule References:\n  \u2022 Source: ACL rule 5 (Ln 107), ACL rule 6 (Ln 112)"}, {"color": "#ff6666", "id": "production-backend:*", "label": "production-backend:*", "shape": "dot", "title": "production-backend:*\n\u2139\ufe0f  *************/24\n\ud83d\udd12 Legacy ACL Rules\n\ud83d\udccb Rule References:\n  \u2022 Destination: ACL rule 12 (Ln 152)"}, {"color": "#ff6666", "id": "web-server:443", "label": "web-server:443", "shape": "dot", "title": "web-server:443\n\u2139\ufe0f  ***************\n\ud83d\udd12 Legacy ACL Rules\n\ud83d\udccb Rule References:\n  \u2022 Destination: ACL rule 11 (Ln 145), ACL rule 12 (Ln 152)"}, {"color": "#00cc66", "id": "tag:database:*", "label": "tag:database:*", "shape": "dot", "title": "tag:database:*\n\ud83d\udd12 Legacy ACL Rules\n\ud83d\udccb Rule References:\n  \u2022 Destination: ACL rule 10 (Ln 140), ACL rule 12 (Ln 152)"}, {"color": "#FFFF00", "id": "autogroup:self:*", "label": "autogroup:self:*", "shape": "dot", "title": "autogroup:self:*\n\ud83d\udd12 Legacy ACL Rules\n\ud83d\udccb Rule References:\n  \u2022 Destination: ACL rule 3 (Ln 95)"}, {"color": "#00cc66", "id": "tag:logging", "label": "tag:logging", "shape": "triangle", "title": "tag:logging\n\ud83c\udfaf Modern Grant Rules\n\ud83d\udccb Rule References:\n  \u2022 Source: Grant rule 15 (Ln 264)\n  \u2022 Destination: Grant rule 5 (Ln 190)\n\ud83c\udf10 Protocols: tcp:9100, udp:514, tcp:8080, tcp:514, udp:161"}, {"color": "#00cc66", "id": "tag:security", "label": "tag:security", "shape": "dot", "title": "tag:security\n\ud83d\udd12 Legacy ACL Rules\n\ud83d\udccb Rule References:\n  \u2022 Source: ACL rule 1 (Ln 81)"}, {"color": "#00cc66", "id": "tag:backend [tcp:15000-15010,9080]", "label": "tag:backend [tcp:15000-15010,9080]", "shape": "triangle", "title": "tag:backend [tcp:15000-15010,9080]"}, {"color": "#FFFF00", "id": "autogroup:internet", "label": "autogroup:internet", "shape": "triangle", "title": "autogroup:internet\n\ud83c\udfaf Modern Grant Rules\n\ud83d\udccb Rule References:\n  \u2022 Destination: Grant rule 3 (Ln 178), Grant rule 8 (Ln 209)"}, {"color": "#FFFF00", "id": "group:system_admin", "label": "group:system_admin", "shape": "dot", "title": "group:system_admin\n\ud83d\udd12 Legacy ACL Rules\n\ud83d\udccb Rule References:\n  \u2022 Source: ACL rule 2 (Ln 88)\n\ud83d\udc65 Group Members: <EMAIL>, <EMAIL>"}, {"color": "#ff6666", "id": "*", "label": "*", "shape": "triangle", "title": "*\n\ud83c\udfaf Modern Grant Rules\n\ud83d\udccb Rule References:\n  \u2022 Source: Grant rule 1 (Ln 168)\n  \u2022 Destination: Grant rule 1 (Ln 168), Grant rule 16 (Ln 271)\n\ud83c\udf10 Protocols: *, tcp:22"}, {"color": "#FFFF00", "id": "group:dba", "label": "group:dba", "shape": "dot", "title": "group:dba\n\ud83d\udd12 Legacy ACL Rules\n\ud83d\udccb Rule References:\n  \u2022 Source: ACL rule 7 (Ln 119)\n\ud83d\udc65 Group Members: <EMAIL>"}, {"color": "#00cc66", "id": "tag:backend [udp:161, tcp:9100,8080]", "label": "tag:backend [udp:161, tcp:9100,8080]", "shape": "triangle", "title": "tag:backend [udp:161, tcp:9100,8080]"}, {"color": "#00cc66", "id": "tag:internal-tools", "label": "tag:internal-tools", "shape": "triangle", "title": "tag:internal-tools\n\ud83c\udfaf Modern Grant Rules\n\ud83d\udccb Rule References:\n  \u2022 Destination: Grant rule 6 (Ln 197), Grant rule 7 (Ln 202)\n\ud83c\udf10 Protocols: tcp:443, *, tcp:22"}, {"color": "#FFFF00", "id": "autogroup:self", "label": "autogroup:self", "shape": "triangle", "title": "autogroup:self\n\ud83c\udfaf Modern Grant Rules\n\ud83d\udccb Rule References:\n  \u2022 Destination: Grant rule 2 (Ln 173)"}, {"color": "#00cc66", "id": "tag:backend [tcp:8080,443]", "label": "tag:backend [tcp:8080,443]", "shape": "triangle", "title": "tag:backend [tcp:8080,443]"}, {"color": "#FFFF00", "id": "autogroup:member", "label": "autogroup:member", "shape": "hexagon", "title": "autogroup:member\n\ud83d\udd04 Mixed (ACL + Grant Rules)\n\ud83d\udccb Rule References:\n  \u2022 Source: ACL rule 3 (Ln 95), Grant rule 2 (Ln 173), Grant rule 3 (Ln 178), Grant rule 20 (Ln 299)\n\ud83d\udee4\ufe0f  Via Routes: tag:exit-node-nyc"}, {"color": "#00cc66", "id": "tag:frontend [udp:161, tcp:9100,8080]", "label": "tag:frontend [udp:161, tcp:9100,8080]", "shape": "triangle", "title": "tag:frontend [udp:161, tcp:9100,8080]"}, {"color": "#00cc66", "id": "tag:backend", "label": "tag:backend", "shape": "triangle", "title": "tag:backend\n\ud83c\udfaf Modern Grant Rules\n\ud83d\udccb Rule References:\n  \u2022 Source: Grant rule 5 (Ln 190), Grant rule 14 (Ln 257)\n  \u2022 Destination: Grant rule 4 (Ln 185), Grant rule 10 (Ln 224), Grant rule 14 (Ln 257), Grant rule 15 (Ln 264)\n\ud83c\udf10 Protocols: tcp:9090, tcp:9100, udp:514, tcp:8080, tcp:514, tcp:9080, udp:161, tcp:443, tcp:15000-15010\n\ud83d\udcf1 Applications: example.com/webapp-connector"}, {"color": "#FFFF00", "id": "group:dev", "label": "group:dev", "shape": "triangle", "title": "group:dev\n\ud83c\udfaf Modern Grant Rules\n\ud83d\udccb Rule References:\n  \u2022 Source: Grant rule 18 (Ln 289)\n\ud83d\udc65 Group Members: <EMAIL>, <EMAIL>"}, {"color": "#00cc66", "id": "tag:internal-tools [tcp:443,22]", "label": "tag:internal-tools [tcp:443,22]", "shape": "triangle", "title": "tag:internal-tools [tcp:443,22]"}, {"color": "#FFFF00", "id": "group:contractors", "label": "group:contractors", "shape": "triangle", "title": "group:contractors\n\ud83c\udfaf Modern Grant Rules\n\ud83d\udccb Rule References:\n  \u2022 Source: Grant rule 12 (Ln 242)\n\ud83c\udf10 Protocols: tcp:80, tcp:22\n\ud83d\udc65 Group Members: <EMAIL>"}, {"color": "#00cc66", "id": "tag:frontend", "label": "tag:frontend", "shape": "triangle", "title": "tag:frontend\n\ud83c\udfaf Modern Grant Rules\n\ud83d\udccb Rule References:\n  \u2022 Source: Grant rule 4 (Ln 185), Grant rule 10 (Ln 224)\n  \u2022 Destination: Grant rule 15 (Ln 264)\n\ud83c\udf10 Protocols: tcp:9090, tcp:9100, tcp:8080, udp:161, tcp:443\n\ud83d\udcf1 Applications: example.com/webapp-connector"}, {"color": "#00cc66", "id": "tag:dev", "label": "tag:dev", "shape": "triangle", "title": "tag:dev\n\ud83c\udfaf Modern Grant Rules\n\ud83d\udccb Rule References:\n  \u2022 Destination: Grant rule 18 (Ln 289)"}, {"color": "#00cc66", "id": "tag:production [tcp:443,8443]", "label": "tag:production [tcp:443,8443]", "shape": "triangle", "title": "tag:production [tcp:443,8443]"}, {"color": "#00cc66", "id": "tag:backend [tcp:8080,9090]", "label": "tag:backend [tcp:8080,9090]", "shape": "triangle", "title": "tag:backend [tcp:8080,9090]"}, {"color": "#FFFF00", "id": "group:all-staff:*", "label": "group:all-staff:*", "shape": "dot", "title": "group:all-staff:*\n\ud83d\udd12 Legacy ACL Rules\n\ud83d\udccb Rule References:\n  \u2022 Destination: ACL rule 5 (Ln 107)"}, {"color": "#FFFF00", "id": "group:dev-team", "label": "group:dev-team", "shape": "dot", "title": "group:dev-team\n\ud83d\udd12 Legacy ACL Rules\n\ud83d\udccb Rule References:\n  \u2022 Source: ACL rule 8 (Ln 126)\n\ud83d\udc65 Group Members: <EMAIL>"}, {"color": "#FFFF00", "id": "group:mobile", "label": "group:mobile", "shape": "triangle", "title": "group:mobile\n\ud83c\udfaf Modern Grant Rules\n\ud83d\udccb Rule References:\n  \u2022 Source: Grant rule 9 (Ln 216)\n\ud83c\udf10 Protocols: tcp:443\n\ud83d\udd10 Posture Checks: posture:latestMac\n\ud83d\udc65 Group Members: <EMAIL>"}, {"color": "#ff6666", "id": "database [tcp:5432,3306]", "label": "database [tcp:5432,3306]", "shape": "triangle", "title": "database [tcp:5432,3306]"}, {"color": "#00cc66", "id": "tag:production [tcp:443]", "label": "tag:production [tcp:443]", "shape": "triangle", "title": "tag:production [tcp:443]"}, {"color": "#00cc66", "id": "tag:ci", "label": "tag:ci", "shape": "hexagon", "title": "tag:ci\n\ud83d\udd04 Mixed (ACL + Grant Rules)\n\ud83d\udccb Rule References:\n  \u2022 Source: ACL rule 8 (Ln 126)\n  \u2022 Destination: Grant rule 19 (Ln 294)"}, {"color": "#FFFF00", "id": "group:eng", "label": "group:eng", "shape": "triangle", "title": "group:eng\n\ud83c\udfaf Modern Grant Rules\n\ud83d\udccb Rule References:\n  \u2022 Source: Grant rule 6 (Ln 197), Grant rule 13 (Ln 249)\n\ud83c\udf10 Protocols: tcp:5432, *, tcp:3306\n\ud83d\udd10 Posture Checks: posture:complianceDevice\n\ud83d\udc65 Group Members: <EMAIL>, <EMAIL>"}, {"color": "#ff6666", "id": "* [tcp:22]", "label": "* [tcp:22]", "shape": "triangle", "title": "* [tcp:22]"}, {"color": "#00cc66", "id": "tag:database:5432", "label": "tag:database:5432", "shape": "dot", "title": "tag:database:5432\n\ud83d\udd12 Legacy ACL Rules\n\ud83d\udccb Rule References:\n  \u2022 Destination: ACL rule 7 (Ln 119)"}, {"color": "#ff6666", "id": "*:*", "label": "*:*", "shape": "dot", "title": "*:*\n\ud83d\udd12 Legacy ACL Rules\n\ud83d\udccb Rule References:\n  \u2022 Destination: ACL rule 1 (Ln 81), ACL rule 2 (Ln 88)"}, {"color": "#00cc66", "id": "tag:prod", "label": "tag:prod", "shape": "hexagon", "title": "tag:prod\n\ud83d\udd04 Mixed (ACL + Grant Rules)\n\ud83d\udccb Rule References:\n  \u2022 Source: ACL rule 9 (Ln 133)\n  \u2022 Destination: Grant rule 19 (Ln 294)"}, {"color": "#FFFF00", "id": "group:nyc", "label": "group:nyc", "shape": "triangle", "title": "group:nyc\n\ud83c\udfaf Modern Grant Rules\n\ud83d\udccb Rule References:\n  \u2022 Source: Grant rule 8 (Ln 209)\n\ud83d\udc65 Group Members: <EMAIL>"}, {"color": "#00cc66", "id": "tag:webserver:*", "label": "tag:webserver:*", "shape": "dot", "title": "tag:webserver:*\n\ud83d\udd12 Legacy ACL Rules\n\ud83d\udccb Rule References:\n  \u2022 Destination: ACL rule 12 (Ln 152)"}, {"color": "#00cc66", "id": "tag:production", "label": "tag:production", "shape": "triangle", "title": "tag:production\n\ud83c\udfaf Modern Grant Rules\n\ud83d\udccb Rule References:\n  \u2022 Destination: Grant rule 9 (Ln 216), Grant rule 11 (Ln 234), Grant rule 17 (Ln 278)\n\ud83c\udf10 Protocols: tcp:443, *, tcp:8443\n\ud83d\udcf1 Applications: example.com/deployment-pipeline"}, {"color": "#00cc66", "id": "tag:database", "label": "tag:database", "shape": "dot", "title": "tag:database\n\ud83d\udd12 Legacy ACL Rules\n\ud83d\udccb Rule References:\n  \u2022 Source: ACL rule 7 (Ln 119)"}, {"color": "#FFFF00", "id": "group:sre", "label": "group:sre", "shape": "dot", "title": "group:sre\n\ud83d\udd12 Legacy ACL Rules\n\ud83d\udccb Rule References:\n  \u2022 Source: ACL rule 10 (Ln 140), ACL rule 12 (Ln 152)\n\ud83d\udc65 Group Members: <EMAIL>"}]);
                  edges = new vis.DataSet([{"arrows": {"to": {"enabled": true}}, "from": "group:sre", "to": "tag:webserver:*"}, {"arrows": {"to": {"enabled": true}}, "from": "tag:domain-controller", "to": "tag:domain-controller:*"}, {"arrows": {"to": {"enabled": true}}, "from": "tag:security", "to": "*:*"}, {"arrows": {"to": {"enabled": true}}, "from": "tag:logging", "to": "tag:backend [udp:161, tcp:9100,8080]"}, {"arrows": {"to": {"enabled": true}}, "from": "tag:database", "to": "tag:database:5432"}, {"arrows": {"to": {"enabled": true}}, "from": "tag:staging", "to": "tag:production [tcp:443,8443]"}, {"arrows": {"to": {"enabled": true}}, "from": "tag:ci", "to": "uat1:22"}, {"arrows": {"to": {"enabled": true}}, "from": "autogroup:member", "to": "autogroup:self:*"}, {"arrows": {"to": {"enabled": true}}, "from": "autogroup:member", "to": "*********/24"}, {"arrows": {"to": {"enabled": true}}, "from": "group:system_admin", "to": "*:*"}, {"arrows": {"to": {"enabled": true}}, "from": "group:all-staff", "to": "tag:domain-controller:*"}, {"arrows": {"to": {"enabled": true}}, "from": "group:sre", "to": "production-backend:*"}, {"arrows": {"to": {"enabled": true}}, "from": "group:dev", "to": "tag:dev"}, {"arrows": {"to": {"enabled": true}}, "from": "group:devops", "to": "tag:ci"}, {"arrows": {"to": {"enabled": true}}, "from": "group:devops", "to": "tag:prod"}, {"arrows": {"to": {"enabled": true}}, "from": "tag:backend", "to": "tag:logging [tcp:514, udp:514]"}, {"arrows": {"to": {"enabled": true}}, "from": "group:dba", "to": "tag:database:5432"}, {"arrows": {"to": {"enabled": true}}, "from": "tag:frontend", "to": "tag:backend [tcp:8080,443]"}, {"arrows": {"to": {"enabled": true}}, "from": "tag:prod", "to": "tag:prod:*"}, {"arrows": {"to": {"enabled": true}}, "from": "autogroup:member", "to": "autogroup:internet"}, {"arrows": {"to": {"enabled": true}}, "from": "tag:domain-controller", "to": "group:all-staff:*"}, {"arrows": {"to": {"enabled": true}}, "from": "group:sre", "to": "web-server:443"}, {"arrows": {"to": {"enabled": true}}, "from": "*", "to": "*"}, {"arrows": {"to": {"enabled": true}}, "from": "tag:frontend", "to": "tag:backend [tcp:8080,9090]"}, {"arrows": {"to": {"enabled": true}}, "from": "tag:webserver", "to": "web-server:443"}, {"arrows": {"to": {"enabled": true}}, "from": "group:sre", "to": "uat1:22"}, {"arrows": {"to": {"enabled": true}}, "from": "group:eng", "to": "database [tcp:5432,3306]"}, {"arrows": {"to": {"enabled": true}}, "from": "group:sre", "to": "tag:database:*"}, {"arrows": {"to": {"enabled": true}}, "from": "tag:logging", "to": "tag:frontend [udp:161, tcp:9100,8080]"}, {"arrows": {"to": {"enabled": true}}, "from": "group:eng", "to": "tag:internal-tools"}, {"arrows": {"to": {"enabled": true}}, "from": "group:contractors", "to": "tag:staging [tcp:22,80]"}, {"arrows": {"to": {"enabled": true}}, "from": "tag:webserver", "to": "tag:database:*"}, {"arrows": {"to": {"enabled": true}}, "from": "group:nyc", "to": "autogroup:internet"}, {"arrows": {"to": {"enabled": true}}, "from": "group:admin", "to": "* [tcp:22]"}, {"arrows": {"to": {"enabled": true}}, "from": "autogroup:member", "to": "autogroup:self"}, {"arrows": {"to": {"enabled": true}}, "from": "group:mobile", "to": "tag:production [tcp:443]"}, {"arrows": {"to": {"enabled": true}}, "from": "group:sales", "to": "tag:internal-tools [tcp:443,22]"}, {"arrows": {"to": {"enabled": true}}, "from": "tag:backend", "to": "tag:backend [tcp:15000-15010,9080]"}, {"arrows": {"to": {"enabled": true}}, "from": "group:admin", "to": "tag:production"}, {"arrows": {"to": {"enabled": true}}, "from": "group:dev-team", "to": "uat1:22"}]);

                  nodeColors = {};
                  allNodes = nodes.get({ returnType: "Object" });
                  for (nodeId in allNodes) {
                    nodeColors[nodeId] = allNodes[nodeId].color;
                  }
                  allEdges = edges.get({ returnType: "Object" });
                  // adding nodes and edges to the graph
                  data = {nodes: nodes, edges: edges};

                  var options = {
    "configure": {
        "enabled": false
    },
    "edges": {
        "color": {
            "inherit": true
        },
        "smooth": {
            "enabled": true,
            "type": "dynamic"
        }
    },
    "interaction": {
        "dragNodes": true,
        "hideEdgesOnDrag": false,
        "hideNodesOnDrag": false,
        "zoomSpeed": 0.25,
        "zoomView": true
    },
    "physics": {
        "enabled": true,
        "stabilization": {
            "enabled": true,
            "fit": true,
            "iterations": 1000,
            "onlyDynamicEdges": false,
            "updateInterval": 50
        }
    }
};

                  


                  

                  network = new vis.Network(container, data, options);

                  
                    network.on("click", neighbourhoodHighlight);
                  

                  
                    network.on("selectNode", neighbourhoodHighlight);
                  

                  


                  

                  return network;

              }
              drawGraph();
        </script>
    
<script>
// Enhanced search metadata
const searchMetadata = {"nodes": {"tag:security": {"node_id": "tag:security", "rule_type": "ACL", "src_rules": ["ACL rule 1 (Ln 81)"], "dst_rules": [], "protocols": [], "via": [], "posture": [], "apps": [], "members": []}, "*:*": {"node_id": "*:*", "rule_type": "ACL", "src_rules": [], "dst_rules": ["ACL rule 1 (Ln 81)", "ACL rule 2 (Ln 88)"], "protocols": [], "via": [], "posture": [], "apps": [], "members": []}, "group:system_admin": {"node_id": "group:system_admin", "rule_type": "ACL", "src_rules": ["ACL rule 2 (Ln 88)"], "dst_rules": [], "protocols": [], "via": [], "posture": [], "apps": [], "members": ["<EMAIL>", "<EMAIL>"]}, "autogroup:member": {"node_id": "autogroup:member", "rule_type": "Mixed", "src_rules": ["ACL rule 3 (Ln 95)", "Grant rule 2 (Ln 173)", "Grant rule 3 (Ln 178)", "Grant rule 20 (Ln 299)"], "dst_rules": [], "protocols": ["*", "*", "*"], "via": ["tag:exit-node-nyc"], "posture": [], "apps": [], "members": []}, "autogroup:self:*": {"node_id": "autogroup:self:*", "rule_type": "ACL", "src_rules": [], "dst_rules": ["ACL rule 3 (Ln 95)"], "protocols": [], "via": [], "posture": [], "apps": [], "members": []}, "group:all-staff": {"node_id": "group:all-staff", "rule_type": "ACL", "src_rules": ["ACL rule 4 (Ln 102)"], "dst_rules": [], "protocols": [], "via": [], "posture": [], "apps": [], "members": ["<EMAIL>"]}, "tag:domain-controller:*": {"node_id": "tag:domain-controller:*", "rule_type": "ACL", "src_rules": [], "dst_rules": ["ACL rule 4 (Ln 102)", "ACL rule 6 (Ln 112)"], "protocols": [], "via": [], "posture": [], "apps": [], "members": []}, "tag:domain-controller": {"node_id": "tag:domain-controller", "rule_type": "ACL", "src_rules": ["ACL rule 5 (Ln 107)", "ACL rule 6 (Ln 112)"], "dst_rules": [], "protocols": [], "via": [], "posture": [], "apps": [], "members": []}, "group:all-staff:*": {"node_id": "group:all-staff:*", "rule_type": "ACL", "src_rules": [], "dst_rules": ["ACL rule 5 (Ln 107)"], "protocols": [], "via": [], "posture": [], "apps": [], "members": []}, "group:dba": {"node_id": "group:dba", "rule_type": "ACL", "src_rules": ["ACL rule 7 (Ln 119)"], "dst_rules": [], "protocols": [], "via": [], "posture": [], "apps": [], "members": ["<EMAIL>"]}, "tag:database": {"node_id": "tag:database", "rule_type": "ACL", "src_rules": ["ACL rule 7 (Ln 119)"], "dst_rules": [], "protocols": [], "via": [], "posture": [], "apps": [], "members": []}, "tag:database:5432": {"node_id": "tag:database:5432", "rule_type": "ACL", "src_rules": [], "dst_rules": ["ACL rule 7 (Ln 119)"], "protocols": [], "via": [], "posture": [], "apps": [], "members": []}, "group:dev-team": {"node_id": "group:dev-team", "rule_type": "ACL", "src_rules": ["ACL rule 8 (Ln 126)"], "dst_rules": [], "protocols": [], "via": [], "posture": [], "apps": [], "members": ["<EMAIL>"]}, "tag:ci": {"node_id": "tag:ci", "rule_type": "Mixed", "src_rules": ["ACL rule 8 (Ln 126)"], "dst_rules": ["Grant rule 19 (Ln 294)"], "protocols": ["*"], "via": [], "posture": [], "apps": [], "members": []}, "uat1:22": {"node_id": "uat1:22", "rule_type": "ACL", "src_rules": [], "dst_rules": ["ACL rule 8 (Ln 126)", "ACL rule 12 (Ln 152)"], "protocols": [], "via": [], "posture": [], "apps": [], "members": []}, "tag:prod": {"node_id": "tag:prod", "rule_type": "Mixed", "src_rules": ["ACL rule 9 (Ln 133)"], "dst_rules": ["Grant rule 19 (Ln 294)"], "protocols": ["*"], "via": [], "posture": [], "apps": [], "members": []}, "tag:prod:*": {"node_id": "tag:prod:*", "rule_type": "ACL", "src_rules": [], "dst_rules": ["ACL rule 9 (Ln 133)"], "protocols": [], "via": [], "posture": [], "apps": [], "members": []}, "tag:webserver": {"node_id": "tag:webserver", "rule_type": "ACL", "src_rules": ["ACL rule 10 (Ln 140)", "ACL rule 11 (Ln 145)"], "dst_rules": [], "protocols": [], "via": [], "posture": [], "apps": [], "members": []}, "group:sre": {"node_id": "group:sre", "rule_type": "ACL", "src_rules": ["ACL rule 10 (Ln 140)", "ACL rule 12 (Ln 152)"], "dst_rules": [], "protocols": [], "via": [], "posture": [], "apps": [], "members": ["<EMAIL>"]}, "tag:database:*": {"node_id": "tag:database:*", "rule_type": "ACL", "src_rules": [], "dst_rules": ["ACL rule 10 (Ln 140)", "ACL rule 12 (Ln 152)"], "protocols": [], "via": [], "posture": [], "apps": [], "members": []}, "web-server:443": {"node_id": "web-server:443", "rule_type": "ACL", "src_rules": [], "dst_rules": ["ACL rule 11 (Ln 145)", "ACL rule 12 (Ln 152)"], "protocols": [], "via": [], "posture": [], "apps": [], "members": []}, "production-backend:*": {"node_id": "production-backend:*", "rule_type": "ACL", "src_rules": [], "dst_rules": ["ACL rule 12 (Ln 152)"], "protocols": [], "via": [], "posture": [], "apps": [], "members": []}, "tag:webserver:*": {"node_id": "tag:webserver:*", "rule_type": "ACL", "src_rules": [], "dst_rules": ["ACL rule 12 (Ln 152)"], "protocols": [], "via": [], "posture": [], "apps": [], "members": []}, "*": {"node_id": "*", "rule_type": "Grant", "src_rules": ["Grant rule 1 (Ln 168)"], "dst_rules": ["Grant rule 1 (Ln 168)", "Grant rule 16 (Ln 271)"], "protocols": ["*", "*", "tcp:22"], "via": [], "posture": [], "apps": [], "members": []}, "autogroup:self": {"node_id": "autogroup:self", "rule_type": "Grant", "src_rules": [], "dst_rules": ["Grant rule 2 (Ln 173)"], "protocols": ["*"], "via": [], "posture": [], "apps": [], "members": []}, "autogroup:internet": {"node_id": "autogroup:internet", "rule_type": "Grant", "src_rules": [], "dst_rules": ["Grant rule 3 (Ln 178)", "Grant rule 8 (Ln 209)"], "protocols": ["*", "*"], "via": [], "posture": [], "apps": [], "members": []}, "tag:frontend": {"node_id": "tag:frontend", "rule_type": "Grant", "src_rules": ["Grant rule 4 (Ln 185)", "Grant rule 10 (Ln 224)"], "dst_rules": ["Grant rule 15 (Ln 264)"], "protocols": ["tcp:8080", "tcp:443", "tcp:8080", "tcp:9090", "udp:161", "tcp:9100", "tcp:8080"], "via": [], "posture": [], "apps": ["example.com/webapp-connector"], "members": []}, "tag:backend": {"node_id": "tag:backend", "rule_type": "Grant", "src_rules": ["Grant rule 5 (Ln 190)", "Grant rule 14 (Ln 257)"], "dst_rules": ["Grant rule 4 (Ln 185)", "Grant rule 10 (Ln 224)", "Grant rule 14 (Ln 257)", "Grant rule 15 (Ln 264)"], "protocols": ["tcp:8080", "tcp:443", "tcp:514", "udp:514", "tcp:8080", "tcp:9090", "tcp:15000-15010", "tcp:9080", "tcp:15000-15010", "tcp:9080", "udp:161", "tcp:9100", "tcp:8080"], "via": [], "posture": [], "apps": ["example.com/webapp-connector"], "members": []}, "tag:logging": {"node_id": "tag:logging", "rule_type": "Grant", "src_rules": ["Grant rule 15 (Ln 264)"], "dst_rules": ["Grant rule 5 (Ln 190)"], "protocols": ["tcp:514", "udp:514", "udp:161", "tcp:9100", "tcp:8080"], "via": [], "posture": [], "apps": [], "members": []}, "group:eng": {"node_id": "group:eng", "rule_type": "Grant", "src_rules": ["Grant rule 6 (Ln 197)", "Grant rule 13 (Ln 249)"], "dst_rules": [], "protocols": ["*", "tcp:5432", "tcp:3306"], "via": [], "posture": ["posture:complianceDevice"], "apps": [], "members": ["<EMAIL>", "<EMAIL>"]}, "tag:internal-tools": {"node_id": "tag:internal-tools", "rule_type": "Grant", "src_rules": [], "dst_rules": ["Grant rule 6 (Ln 197)", "Grant rule 7 (Ln 202)"], "protocols": ["*", "tcp:443", "tcp:22"], "via": [], "posture": [], "apps": [], "members": []}, "group:sales": {"node_id": "group:sales", "rule_type": "Grant", "src_rules": ["Grant rule 7 (Ln 202)"], "dst_rules": [], "protocols": ["tcp:443", "tcp:22"], "via": [], "posture": [], "apps": [], "members": ["<EMAIL>", "<EMAIL>"]}, "group:nyc": {"node_id": "group:nyc", "rule_type": "Grant", "src_rules": ["Grant rule 8 (Ln 209)"], "dst_rules": [], "protocols": ["*"], "via": [], "posture": [], "apps": [], "members": ["<EMAIL>"]}, "group:mobile": {"node_id": "group:mobile", "rule_type": "Grant", "src_rules": ["Grant rule 9 (Ln 216)"], "dst_rules": [], "protocols": ["tcp:443"], "via": [], "posture": ["posture:latestMac"], "apps": [], "members": ["<EMAIL>"]}, "tag:production": {"node_id": "tag:production", "rule_type": "Grant", "src_rules": [], "dst_rules": ["Grant rule 9 (Ln 216)", "Grant rule 11 (Ln 234)", "Grant rule 17 (Ln 278)"], "protocols": ["tcp:443", "*", "tcp:443", "tcp:8443"], "via": [], "posture": [], "apps": ["example.com/deployment-pipeline"], "members": []}, "group:admin": {"node_id": "group:admin", "rule_type": "Grant", "src_rules": ["Grant rule 11 (Ln 234)", "Grant rule 16 (Ln 271)"], "dst_rules": [], "protocols": ["*", "tcp:22"], "via": [], "posture": ["posture:complianceDevice"], "apps": [], "members": ["<EMAIL>"]}, "group:contractors": {"node_id": "group:contractors", "rule_type": "Grant", "src_rules": ["Grant rule 12 (Ln 242)"], "dst_rules": [], "protocols": ["tcp:22", "tcp:80"], "via": [], "posture": [], "apps": [], "members": ["<EMAIL>"]}, "tag:staging": {"node_id": "tag:staging", "rule_type": "Grant", "src_rules": ["Grant rule 17 (Ln 278)"], "dst_rules": ["Grant rule 12 (Ln 242)"], "protocols": ["tcp:22", "tcp:80", "tcp:443", "tcp:8443"], "via": [], "posture": ["posture:complianceDevice"], "apps": ["example.com/deployment-pipeline"], "members": []}, "database": {"node_id": "database", "rule_type": "Grant", "src_rules": [], "dst_rules": ["Grant rule 13 (Ln 249)"], "protocols": ["tcp:5432", "tcp:3306"], "via": [], "posture": [], "apps": [], "members": []}, "group:dev": {"node_id": "group:dev", "rule_type": "Grant", "src_rules": ["Grant rule 18 (Ln 289)"], "dst_rules": [], "protocols": ["*"], "via": [], "posture": [], "apps": [], "members": ["<EMAIL>", "<EMAIL>"]}, "tag:dev": {"node_id": "tag:dev", "rule_type": "Grant", "src_rules": [], "dst_rules": ["Grant rule 18 (Ln 289)"], "protocols": ["*"], "via": [], "posture": [], "apps": [], "members": []}, "group:devops": {"node_id": "group:devops", "rule_type": "Grant", "src_rules": ["Grant rule 19 (Ln 294)"], "dst_rules": [], "protocols": ["*"], "via": [], "posture": [], "apps": [], "members": ["<EMAIL>"]}, "*********/24": {"node_id": "*********/24", "rule_type": "Grant", "src_rules": [], "dst_rules": ["Grant rule 20 (Ln 299)"], "protocols": ["*"], "via": ["tag:exit-node-nyc"], "posture": [], "apps": [], "members": []}}, "edges": {"tag:security->*:*": {"src": "tag:security", "dst": "*:*", "rule_type": "ACL", "rule_index": 1, "action": "accept", "protocols": [], "via": [], "posture": [], "apps": []}, "group:system_admin->*:*": {"src": "group:system_admin", "dst": "*:*", "rule_type": "ACL", "rule_index": 2, "action": "accept", "protocols": [], "via": [], "posture": [], "apps": []}, "autogroup:member->autogroup:self:*": {"src": "autogroup:member", "dst": "autogroup:self:*", "rule_type": "ACL", "rule_index": 3, "action": "accept", "protocols": [], "via": [], "posture": [], "apps": []}, "group:all-staff->tag:domain-controller:*": {"src": "group:all-staff", "dst": "tag:domain-controller:*", "rule_type": "ACL", "rule_index": 4, "action": "accept", "protocols": [], "via": [], "posture": [], "apps": []}, "tag:domain-controller->group:all-staff:*": {"src": "tag:domain-controller", "dst": "group:all-staff:*", "rule_type": "ACL", "rule_index": 5, "action": "accept", "protocols": [], "via": [], "posture": [], "apps": []}, "tag:domain-controller->tag:domain-controller:*": {"src": "tag:domain-controller", "dst": "tag:domain-controller:*", "rule_type": "ACL", "rule_index": 6, "action": "accept", "protocols": [], "via": [], "posture": [], "apps": []}, "group:dba->tag:database:5432": {"src": "group:dba", "dst": "tag:database:5432", "rule_type": "ACL", "rule_index": 7, "action": "accept", "protocols": [], "via": [], "posture": [], "apps": []}, "tag:database->tag:database:5432": {"src": "tag:database", "dst": "tag:database:5432", "rule_type": "ACL", "rule_index": 7, "action": "accept", "protocols": [], "via": [], "posture": [], "apps": []}, "group:dev-team->uat1:22": {"src": "group:dev-team", "dst": "uat1:22", "rule_type": "ACL", "rule_index": 8, "action": "accept", "protocols": [], "via": [], "posture": [], "apps": []}, "tag:ci->uat1:22": {"src": "tag:ci", "dst": "uat1:22", "rule_type": "ACL", "rule_index": 8, "action": "accept", "protocols": [], "via": [], "posture": [], "apps": []}, "tag:prod->tag:prod:*": {"src": "tag:prod", "dst": "tag:prod:*", "rule_type": "ACL", "rule_index": 9, "action": "accept", "protocols": [], "via": [], "posture": [], "apps": []}, "tag:webserver->tag:database:*": {"src": "tag:webserver", "dst": "tag:database:*", "rule_type": "ACL", "rule_index": 10, "action": "accept", "protocols": [], "via": [], "posture": [], "apps": []}, "group:sre->tag:database:*": {"src": "group:sre", "dst": "tag:database:*", "rule_type": "ACL", "rule_index": 12, "action": "accept", "protocols": [], "via": [], "posture": [], "apps": []}, "tag:webserver->web-server:443": {"src": "tag:webserver", "dst": "web-server:443", "rule_type": "ACL", "rule_index": 11, "action": "accept", "protocols": [], "via": [], "posture": [], "apps": []}, "group:sre->production-backend:*": {"src": "group:sre", "dst": "production-backend:*", "rule_type": "ACL", "rule_index": 12, "action": "accept", "protocols": [], "via": [], "posture": [], "apps": []}, "group:sre->tag:webserver:*": {"src": "group:sre", "dst": "tag:webserver:*", "rule_type": "ACL", "rule_index": 12, "action": "accept", "protocols": [], "via": [], "posture": [], "apps": []}, "group:sre->web-server:443": {"src": "group:sre", "dst": "web-server:443", "rule_type": "ACL", "rule_index": 12, "action": "accept", "protocols": [], "via": [], "posture": [], "apps": []}, "group:sre->uat1:22": {"src": "group:sre", "dst": "uat1:22", "rule_type": "ACL", "rule_index": 12, "action": "accept", "protocols": [], "via": [], "posture": [], "apps": []}, "*->*": {"src": "*", "dst": "*", "rule_type": "Grant", "rule_index": 1, "protocols": ["*"], "via": [], "posture": [], "apps": []}, "autogroup:member->autogroup:self": {"src": "autogroup:member", "dst": "autogroup:self", "rule_type": "Grant", "rule_index": 2, "protocols": ["*"], "via": [], "posture": [], "apps": []}, "autogroup:member->autogroup:internet": {"src": "autogroup:member", "dst": "autogroup:internet", "rule_type": "Grant", "rule_index": 3, "protocols": ["*"], "via": [], "posture": [], "apps": []}, "tag:frontend->tag:backend [tcp:8080,443]": {"src": "tag:frontend", "dst": "tag:backend [tcp:8080,443]", "rule_type": "Grant", "rule_index": 4, "protocols": ["tcp:8080", "tcp:443"], "via": [], "posture": [], "apps": []}, "tag:backend->tag:logging [tcp:514, udp:514]": {"src": "tag:backend", "dst": "tag:logging [tcp:514, udp:514]", "rule_type": "Grant", "rule_index": 5, "protocols": ["tcp:514", "udp:514"], "via": [], "posture": [], "apps": []}, "group:eng->tag:internal-tools": {"src": "group:eng", "dst": "tag:internal-tools", "rule_type": "Grant", "rule_index": 6, "protocols": ["*"], "via": [], "posture": [], "apps": []}, "group:sales->tag:internal-tools [tcp:443,22]": {"src": "group:sales", "dst": "tag:internal-tools [tcp:443,22]", "rule_type": "Grant", "rule_index": 7, "protocols": ["tcp:443", "tcp:22"], "via": [], "posture": [], "apps": []}, "group:nyc->autogroup:internet": {"src": "group:nyc", "dst": "autogroup:internet", "rule_type": "Grant", "rule_index": 8, "protocols": ["*"], "via": [], "posture": [], "apps": []}, "group:mobile->tag:production [tcp:443]": {"src": "group:mobile", "dst": "tag:production [tcp:443]", "rule_type": "Grant", "rule_index": 9, "protocols": ["tcp:443"], "via": [], "posture": ["posture:latestMac"], "apps": []}, "tag:frontend->tag:backend [tcp:8080,9090]": {"src": "tag:frontend", "dst": "tag:backend [tcp:8080,9090]", "rule_type": "Grant", "rule_index": 10, "protocols": ["tcp:8080", "tcp:9090"], "via": [], "posture": [], "apps": ["example.com/webapp-connector"]}, "group:admin->tag:production": {"src": "group:admin", "dst": "tag:production", "rule_type": "Grant", "rule_index": 11, "protocols": ["*"], "via": [], "posture": ["posture:complianceDevice"], "apps": []}, "group:contractors->tag:staging [tcp:22,80]": {"src": "group:contractors", "dst": "tag:staging [tcp:22,80]", "rule_type": "Grant", "rule_index": 12, "protocols": ["tcp:22", "tcp:80"], "via": [], "posture": [], "apps": []}, "group:eng->database [tcp:5432,3306]": {"src": "group:eng", "dst": "database [tcp:5432,3306]", "rule_type": "Grant", "rule_index": 13, "protocols": ["tcp:5432", "tcp:3306"], "via": [], "posture": ["posture:complianceDevice"], "apps": []}, "tag:backend->tag:backend [tcp:15000-15010,9080]": {"src": "tag:backend", "dst": "tag:backend [tcp:15000-15010,9080]", "rule_type": "Grant", "rule_index": 14, "protocols": ["tcp:15000-15010", "tcp:9080"], "via": [], "posture": [], "apps": []}, "tag:logging->tag:frontend [udp:161, tcp:9100,8080]": {"src": "tag:logging", "dst": "tag:frontend [udp:161, tcp:9100,8080]", "rule_type": "Grant", "rule_index": 15, "protocols": ["udp:161", "tcp:9100", "tcp:8080"], "via": [], "posture": [], "apps": []}, "tag:logging->tag:backend [udp:161, tcp:9100,8080]": {"src": "tag:logging", "dst": "tag:backend [udp:161, tcp:9100,8080]", "rule_type": "Grant", "rule_index": 15, "protocols": ["udp:161", "tcp:9100", "tcp:8080"], "via": [], "posture": [], "apps": []}, "group:admin->* [tcp:22]": {"src": "group:admin", "dst": "* [tcp:22]", "rule_type": "Grant", "rule_index": 16, "protocols": ["tcp:22"], "via": [], "posture": [], "apps": []}, "tag:staging->tag:production [tcp:443,8443]": {"src": "tag:staging", "dst": "tag:production [tcp:443,8443]", "rule_type": "Grant", "rule_index": 17, "protocols": ["tcp:443", "tcp:8443"], "via": [], "posture": ["posture:complianceDevice"], "apps": ["example.com/deployment-pipeline"]}, "group:dev->tag:dev": {"src": "group:dev", "dst": "tag:dev", "rule_type": "Grant", "rule_index": 18, "protocols": ["*"], "via": [], "posture": [], "apps": []}, "group:devops->tag:prod": {"src": "group:devops", "dst": "tag:prod", "rule_type": "Grant", "rule_index": 19, "protocols": ["*"], "via": [], "posture": [], "apps": []}, "group:devops->tag:ci": {"src": "group:devops", "dst": "tag:ci", "rule_type": "Grant", "rule_index": 19, "protocols": ["*"], "via": [], "posture": [], "apps": []}, "autogroup:member->*********/24": {"src": "autogroup:member", "dst": "*********/24", "rule_type": "Grant", "rule_index": 20, "protocols": ["*"], "via": ["tag:exit-node-nyc"], "posture": [], "apps": []}}};

// Enhanced search functionality
let searchActive = false;
let originalNodeColors = {};
let savedViewState = null; // Store zoom/pan state before search

function initializeSearch() {
    // Store original node colors
    const allNodes = nodes.get({ returnType: "Object" });
    for (let nodeId in allNodes) {
        originalNodeColors[nodeId] = allNodes[nodeId].color;
    }
}

function saveCurrentViewState() {
    // Save current zoom and pan state before performing search
    try {
        if (typeof network !== 'undefined' && network.getViewPosition && network.getScale) {
            savedViewState = {
                position: network.getViewPosition(),
                scale: network.getScale()
            };
        }
    } catch (e) {
        console.log('Could not save view state:', e);
        savedViewState = null;
    }
}

function restoreViewState() {
    // Restore saved zoom and pan state or fit to view
    try {
        if (typeof network !== 'undefined') {
            if (savedViewState && network.moveTo) {
                // Restore previous view state
                network.moveTo({
                    position: savedViewState.position,
                    scale: savedViewState.scale,
                    animation: {
                        duration: 1000,
                        easingFunction: 'easeInOutQuad'
                    }
                });
            } else if (network.fit) {
                // Fallback to fit to view
                network.fit({
                    animation: {
                        duration: 1000,
                        easingFunction: 'easeInOutQuad'
                    }
                });
            }
        }
    } catch (e) {
        console.log('Could not restore view state:', e);
    }
}

// Live search dropdown functionality
let searchDropdownTimeout;
let allSearchableNodes = [];
let selectedDropdownIndex = -1; // Track selected dropdown item for keyboard navigation

function initializeSearchableNodes() {
    allSearchableNodes = [];
    for (let nodeId in searchMetadata.nodes) {
        const metadata = searchMetadata.nodes[nodeId];
        allSearchableNodes.push({
            id: nodeId,
            metadata: metadata,
            color: originalNodeColors[nodeId] || '#97C2FC',
            shape: getNodeShape(metadata)
        });
    }
}

function getNodeShape(metadata) {
    if (metadata.rule_type === 'Mixed') return '⬢';
    if (metadata.rule_type === 'Grant') return '▲';
    return '●';
}

function handleSearchInput(searchTerm) {
    if (searchTerm === '') {
        clearSearch();
        hideSearchDropdown();
        showSearchTips();
    } else {
        hideSearchTips();
        updateSearchDropdown(searchTerm);
    }
}

function handleSearchKeyup(event, searchTerm) {
    const dropdown = document.getElementById('search-dropdown');
    const dropdownItems = dropdown.querySelectorAll('.search-dropdown-item:not([style*="cursor: default"])');

    if (event.key === 'ArrowDown') {
        event.preventDefault();
        if (dropdownItems.length > 0) {
            selectedDropdownIndex = Math.min(selectedDropdownIndex + 1, dropdownItems.length - 1);
            updateDropdownSelection(dropdownItems);
        }
    } else if (event.key === 'ArrowUp') {
        event.preventDefault();
        if (dropdownItems.length > 0) {
            selectedDropdownIndex = Math.max(selectedDropdownIndex - 1, -1);
            updateDropdownSelection(dropdownItems);
        }
    } else if (event.key === 'Enter') {
        event.preventDefault();
        if (selectedDropdownIndex >= 0 && dropdownItems.length > selectedDropdownIndex) {
            // Select the highlighted dropdown item
            const selectedItem = dropdownItems[selectedDropdownIndex];
            const nodeId = selectedItem.getAttribute('data-node-id');
            if (nodeId) {
                selectSearchResult(nodeId);
            }
        } else {
            // No item selected, perform regular search
            performEnhancedSearch(searchTerm);
            hideSearchDropdown();
        }
    } else if (event.key === 'Escape') {
        hideSearchDropdown();
        selectedDropdownIndex = -1;
    } else if (searchTerm === '') {
        clearSearch();
        hideSearchDropdown();
        showSearchTips();
        selectedDropdownIndex = -1;
    } else {
        // Reset selection when typing
        selectedDropdownIndex = -1;
    }
}

function updateDropdownSelection(dropdownItems) {
    // Remove previous selection highlighting
    dropdownItems.forEach((item, index) => {
        if (index === selectedDropdownIndex) {
            item.style.backgroundColor = '#e3f2fd';
            item.style.borderLeft = '3px solid #007bff';

            // Scroll the selected item into view
            const dropdown = document.getElementById('search-dropdown');
            const itemTop = item.offsetTop;
            const itemBottom = itemTop + item.offsetHeight;
            const dropdownTop = dropdown.scrollTop;
            const dropdownBottom = dropdownTop + dropdown.clientHeight;

            // Check if item is above visible area
            if (itemTop < dropdownTop) {
                dropdown.scrollTop = itemTop;
            }
            // Check if item is below visible area
            else if (itemBottom > dropdownBottom) {
                dropdown.scrollTop = itemBottom - dropdown.clientHeight;
            }
        } else {
            item.style.backgroundColor = '';
            item.style.borderLeft = '';
        }
    });
}

function updateSearchDropdown(searchTerm) {
    if (!searchTerm || searchTerm.trim() === '') {
        hideSearchDropdown();
        return;
    }

    const term = searchTerm.toLowerCase().trim();
    const matchingNodes = [];

    // Search through all nodes
    for (let node of allSearchableNodes) {
        const metadata = node.metadata;
        let matches = false;
        let matchDetails = [];

        // Search in node ID
        if (node.id.toLowerCase().includes(term)) {
            matches = true;
            matchDetails.push('name');
        }

        // Search in protocols
        if (metadata.protocols && metadata.protocols.length > 0) {
            // If searching for "protocol" or "protocols", match any node that has protocols
            if (term === 'protocol' || term === 'protocols') {
                matches = true;
                matchDetails.push('protocol');
            } else if (metadata.protocols.some(p => p.toLowerCase().includes(term))) {
                // Otherwise, search within the protocol values
                matches = true;
                matchDetails.push('protocol');
            }
        }

        // Search in via routing
        if (metadata.via && metadata.via.length > 0) {
            // If searching for "via", match any node that has via routing
            if (term === 'via') {
                matches = true;
                matchDetails.push('via');
            } else if (metadata.via.some(v => v.toLowerCase().includes(term))) {
                // Otherwise, search within the via route values
                matches = true;
                matchDetails.push('via');
            }
        }

        // Search in posture checks
        if (metadata.posture && metadata.posture.length > 0) {
            // If searching for "posture", match any node that has posture checks
            if (term === 'posture') {
                matches = true;
                matchDetails.push('posture');
            } else if (metadata.posture.some(p => p.toLowerCase().includes(term))) {
                // Otherwise, search within the posture values
                matches = true;
                matchDetails.push('posture');
            }
        }

        // Search in applications
        if (metadata.apps && metadata.apps.length > 0) {
            // If searching for "app" or "apps", match any node that has applications
            if (term === 'app' || term === 'apps') {
                matches = true;
                matchDetails.push('app');
            } else if (metadata.apps.some(a => a.toLowerCase().includes(term))) {
                // Otherwise, search within the app values
                matches = true;
                matchDetails.push('app');
            }
        }

        // Search in group members
        if (metadata.members && metadata.members.some(m => m.toLowerCase().includes(term))) {
            matches = true;
            matchDetails.push('member');
        }

        // Search in rule references
        if (metadata.src_rules && metadata.src_rules.some(r => r.toLowerCase().includes(term))) {
            matches = true;
            matchDetails.push('rule');
        }
        if (metadata.dst_rules && metadata.dst_rules.some(r => r.toLowerCase().includes(term))) {
            matches = true;
            matchDetails.push('rule');
        }

        // Search in rule type
        if (metadata.rule_type && metadata.rule_type.toLowerCase().includes(term)) {
            matches = true;
            matchDetails.push('type');
        }

        if (matches) {
            matchingNodes.push({
                ...node,
                matchDetails: matchDetails
            });
        }
    }

    // Limit results to prevent performance issues
    const limitedResults = matchingNodes.slice(0, 15);
    displaySearchDropdown(limitedResults, term);
}

function displaySearchDropdown(matchingNodes, searchTerm) {
    const dropdown = document.getElementById('search-dropdown');

    if (matchingNodes.length === 0) {
        dropdown.innerHTML = '<div class="search-dropdown-item" style="color: #999; cursor: default;">No matching nodes found</div>';
    } else {
        dropdown.innerHTML = matchingNodes.map(node => {
            const details = node.matchDetails.join(', ');
            return `
                <div class="search-dropdown-item" data-node-id="${node.id}" onclick="selectSearchResult('${node.id}')">
                    <div class="node-indicator" style="background-color: ${node.color};"></div>
                    <div class="node-shape">${node.shape}</div>
                    <div class="node-info">
                        <div class="node-name">${node.id}</div>
                        <div class="node-details">Matches: ${details} | Type: ${node.metadata.rule_type}</div>
                    </div>
                </div>
            `;
        }).join('');
    }

    dropdown.style.display = 'block';
    selectedDropdownIndex = -1; // Reset keyboard navigation selection when dropdown content changes
}

function selectSearchResult(nodeId) {
    // Set the search input to the selected node
    document.getElementById('enhanced-search-input').value = nodeId;

    // Save view state before search if not already saved
    if (!searchActive) {
        saveCurrentViewState();
    }

    // Perform the search to highlight the node
    performEnhancedSearch(nodeId);

    // Hide the dropdown
    hideSearchDropdown();

    // Focus and center on the selected node
    focusOnNode(nodeId);
}

function focusOnNode(nodeId) {
    // Try to focus on the node in the network
    try {
        if (typeof network !== 'undefined' && network.focus) {
            network.focus(nodeId, {
                scale: 1.5,
                animation: {
                    duration: 1000,
                    easingFunction: 'easeInOutQuad'
                }
            });
        }
    } catch (e) {
        console.log('Could not focus on node:', e);
    }
}

function showSearchDropdown() {
    const input = document.getElementById('enhanced-search-input');
    if (input.value.trim() !== '') {
        updateSearchDropdown(input.value);
    }
}

function hideSearchDropdown() {
    document.getElementById('search-dropdown').style.display = 'none';
    selectedDropdownIndex = -1; // Reset keyboard navigation selection
}

function hideSearchDropdownDelayed() {
    // Delay hiding to allow clicking on dropdown items
    searchDropdownTimeout = setTimeout(() => {
        hideSearchDropdown();
    }, 200);
}

function showSearchTips() {
    document.getElementById('search-help').style.display = 'block';
}

function hideSearchTips() {
    document.getElementById('search-help').style.display = 'none';
}

function performEnhancedSearch(searchTerm) {
    if (!searchTerm || searchTerm.trim() === '') {
        clearSearch();
        return;
    }

    // Save current view state before performing search
    if (!searchActive) {
        saveCurrentViewState();
    }

    searchActive = true;
    const term = searchTerm.toLowerCase().trim();
    const allNodes = nodes.get({ returnType: "Object" });
    const matchingNodes = [];



    // Search through node metadata
    for (let nodeId in searchMetadata.nodes) {
        const metadata = searchMetadata.nodes[nodeId];
        let matches = false;

        // Search in node ID
        if (nodeId.toLowerCase().includes(term)) {
            matches = true;
        }

        // Search in protocols
        if (metadata.protocols && metadata.protocols.length > 0) {
            // If searching for "protocol" or "protocols", match any node that has protocols
            if (term === 'protocol' || term === 'protocols') {
                matches = true;
            } else if (metadata.protocols.some(p => p.toLowerCase().includes(term))) {
                // Otherwise, search within the protocol values
                matches = true;
            }
        }

        // Search in via routing
        if (metadata.via && metadata.via.length > 0) {
            // If searching for "via", match any node that has via routing
            if (term === 'via') {
                matches = true;
            } else if (metadata.via.some(v => v.toLowerCase().includes(term))) {
                // Otherwise, search within the via route values
                matches = true;
            }
        }

        // Search in posture checks
        if (metadata.posture && metadata.posture.length > 0) {
            // If searching for "posture", match any node that has posture checks
            if (term === 'posture') {
                matches = true;
            } else if (metadata.posture.some(p => p.toLowerCase().includes(term))) {
                // Otherwise, search within the posture values
                matches = true;
            }
        }

        // Search in apps
        if (metadata.apps && metadata.apps.length > 0) {
            // If searching for "app" or "apps", match any node that has applications
            if (term === 'app' || term === 'apps') {
                matches = true;
            } else if (metadata.apps.some(a => a.toLowerCase().includes(term))) {
                // Otherwise, search within the app values
                matches = true;
            }
        }

        // Search in group members
        if (metadata.members && metadata.members.some(m => m.toLowerCase().includes(term))) {
            matches = true;
        }

        // Search in rule types
        if (metadata.rule_type && metadata.rule_type.toLowerCase().includes(term)) {
            matches = true;
        }

        if (matches) {
            matchingNodes.push(nodeId);
        }
    }

    // Update node appearance
    for (let nodeId in allNodes) {
        if (matchingNodes.includes(nodeId)) {
            // Highlight matching nodes
            allNodes[nodeId].color = '#ff6b6b'; // Bright red for matches
            allNodes[nodeId].borderWidth = 4;
        } else {
            // Dim non-matching nodes
            allNodes[nodeId].color = 'rgba(200,200,200,0.3)';
            allNodes[nodeId].borderWidth = 1;
        }
    }

    // Update the visualization
    const updateArray = Object.values(allNodes);
    nodes.update(updateArray);

    // Update search results count
    document.getElementById('search-results-count').textContent =
        `Found ${matchingNodes.length} matching nodes`;
}

function clearSearch() {
    // Always clear the input box and hide dropdown
    document.getElementById('enhanced-search-input').value = '';
    document.getElementById('search-results-count').textContent = '';
    hideSearchDropdown();
    showSearchTips();

    // Only restore visualization if search was active
    if (!searchActive) return;

    searchActive = false;
    const allNodes = nodes.get({ returnType: "Object" });

    // Restore original appearance
    for (let nodeId in allNodes) {
        allNodes[nodeId].color = originalNodeColors[nodeId];
        allNodes[nodeId].borderWidth = 2;
    }

    // Update the visualization
    const updateArray = Object.values(allNodes);
    nodes.update(updateArray);

    // Restore zoom and pan state
    restoreViewState();

    // Reset saved view state
    savedViewState = null;
}

// Drag functionality for the search container
let isDragging = false;
let dragOffset = { x: 0, y: 0 };

function initializeDragFunctionality() {
    const container = document.getElementById('enhanced-search-container');
    const dragHandle = document.getElementById('drag-handle');

    if (!container || !dragHandle) return;

    // Mouse down event on drag handle
    dragHandle.addEventListener('mousedown', function(e) {
        isDragging = true;
        container.classList.add('dragging');

        const rect = container.getBoundingClientRect();
        dragOffset.x = e.clientX - rect.left;
        dragOffset.y = e.clientY - rect.top;

        // Prevent text selection during drag
        e.preventDefault();
        document.body.style.userSelect = 'none';
    });

    // Mouse move event for dragging
    document.addEventListener('mousemove', function(e) {
        if (!isDragging) return;

        e.preventDefault();

        // Calculate new position
        let newX = e.clientX - dragOffset.x;
        let newY = e.clientY - dragOffset.y;

        // Get viewport dimensions
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        const containerRect = container.getBoundingClientRect();

        // Apply boundary constraints
        newX = Math.max(0, Math.min(newX, viewportWidth - containerRect.width));
        newY = Math.max(0, Math.min(newY, viewportHeight - containerRect.height));

        // Update position
        container.style.left = newX + 'px';
        container.style.top = newY + 'px';
        container.style.bottom = 'auto'; // Remove bottom positioning when dragging
    });

    // Mouse up event to stop dragging
    document.addEventListener('mouseup', function() {
        if (isDragging) {
            isDragging = false;
            container.classList.remove('dragging');
            document.body.style.userSelect = '';
        }
    });

    // Prevent dragging when clicking on input or buttons
    const searchInput = document.getElementById('enhanced-search-input');
    const searchButtons = container.querySelectorAll('.search-btn');

    [searchInput, ...searchButtons].forEach(element => {
        if (element) {
            element.addEventListener('mousedown', function(e) {
                e.stopPropagation();
            });
        }
    });
}

// Initialize search when the network is ready
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(function() {
        initializeSearch();
        initializeSearchableNodes();
        initializeDragFunctionality();
    }, 1000); // Wait for network to be fully loaded
});
</script>

<style>
.enhanced-search-container {
    position: fixed;
    bottom: 20px;
    left: 20px;
    background-color: #ffffff;
    padding: 15px;
    border: 2px solid #007bff;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    z-index: 1000;
    min-width: 300px;
    max-width: 400px;
    cursor: move;
    user-select: none;
}

.enhanced-search-container.dragging {
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
    transform: scale(1.02);
    transition: none;
    opacity: 0.9;
}

.enhanced-search-container:not(.dragging) {
    transition: all 0.2s ease;
}

.enhanced-search-container .drag-handle {
    cursor: move;
    padding: 5px;
    margin: -5px -5px 5px -5px;
    border-radius: 4px 4px 0 0;
    background: linear-gradient(90deg, #007bff, #0056b3);
    color: white;
    font-weight: bold;
    text-align: center;
    font-size: 12px;
}

.search-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    margin-bottom: 8px;
}

.search-input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.search-controls {
    display: flex;
    gap: 8px;
    margin-bottom: 8px;
}

.search-btn {
    padding: 6px 12px;
    border: 1px solid #007bff;
    background-color: #007bff;
    color: white;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
}

.search-btn:hover {
    background-color: #0056b3;
}

.search-btn.secondary {
    background-color: #6c757d;
    border-color: #6c757d;
}

.search-btn.secondary:hover {
    background-color: #545b62;
}

.search-results {
    font-size: 12px;
    color: #666;
    margin-top: 5px;
}

.search-help {
    font-size: 11px;
    color: #888;
    margin-top: 8px;
    line-height: 1.3;
}

.search-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-top: none;
    border-radius: 0 0 4px 4px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.search-dropdown-item {
    padding: 8px 12px;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
    font-size: 12px;
    display: flex;
    align-items: center;
}

.search-dropdown-item:hover {
    background-color: #f8f9fa;
}

.search-dropdown-item:last-child {
    border-bottom: none;
}

.search-dropdown-item .node-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
    border: 1px solid #999;
}

.search-dropdown-item .node-shape {
    margin-right: 8px;
    font-size: 14px;
}

.search-dropdown-item .node-info {
    flex: 1;
}

.search-dropdown-item .node-name {
    font-weight: bold;
    color: #333;
}

.search-dropdown-item .node-details {
    color: #666;
    font-size: 11px;
    margin-top: 2px;
}
</style>

<div class="enhanced-search-container" id="enhanced-search-container">
    <div class="drag-handle" id="drag-handle">Search (Drag to Move)</div>

    <div style="position: relative;">
        <input type="text"
               id="enhanced-search-input"
               class="search-input"
               placeholder="Type to filter..."
               onkeyup="handleSearchKeyup(event, this.value)"
               oninput="handleSearchInput(this.value)"
               onfocus="showSearchDropdown()"
               onblur="hideSearchDropdownDelayed()">

        <div id="search-dropdown" class="search-dropdown">
            <!-- Dropdown items will be populated by JavaScript -->
        </div>
    </div>

    <div class="search-controls">
        <button class="search-btn" onclick="performEnhancedSearch(document.getElementById('enhanced-search-input').value)">
            Search
        </button>
        <button class="search-btn secondary" onclick="clearSearch()">
            Clear
        </button>
    </div>

    <div id="search-results-count" class="search-results"></div>

    <div id="search-help" class="search-help">
        <strong>Search tips:</strong><br>
        • Node names: tag:prod, group:admin<br>
        • Group members: dev1, <EMAIL><br>
        • Protocols: tcp:443, udp:161<br>
        • Via routes: exit-node, api-gateway<br>
        • Posture: compliance, latest<br>
        • Rule types: ACL, Grant, Mixed
    </div>
</div>

<script>
// Global TomSelect instance for reset functionality
var selectNodeTomSelect;

// Initialize TomSelect when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    const selectElement = document.getElementById('select-node');
    if (selectElement) {
        // Update the first option to be a proper placeholder
        const firstOption = selectElement.querySelector('option:first-child');
        if (firstOption) {
            firstOption.setAttribute('disabled', 'disabled');
            firstOption.setAttribute('selected', 'selected');
            firstOption.setAttribute('value', '');
            firstOption.textContent = 'Select a Node by ID';
        }

        // Initialize TomSelect
        selectNodeTomSelect = new TomSelect("#select-node", {
            create: false,
            placeholder: "Select a Node by ID",
            sortField: {
                field: "text",
                direction: "asc"
            }
        });
    }
});

// Enhanced reset function
function resetSelection() {
    // Reset visual selection
    neighbourhoodHighlight({nodes: []});

    // Reset TomSelect component to show placeholder
    if (selectNodeTomSelect) {
        selectNodeTomSelect.clear();
    } else {
        // Fallback: reset the original select element
        const selectElement = document.getElementById('select-node');
        if (selectElement) {
            selectElement.selectedIndex = 0;
        }
    }
}
</script>
</body>
</html>
<!-- Sliding Legend Panel -->
<div id="legend-panel" style="position: fixed; top: 90px; right: 0; width: 300px; height: calc(100vh - 90px); background-color: #f5f5f5; border-left: 1px solid #ccc; font-family: Arial, sans-serif; font-size: 12px; z-index: 1000; transform: translateX(0); transition: transform 0.3s ease-in-out; overflow-y: auto; box-shadow: -2px 0 5px rgba(0,0,0,0.1);">
    <div style="padding: 20px;">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
            <h3 style="margin: 0; font-size: 16px; color: #333;">Legend</h3>
            <button id="legend-close" style="background: none; border: none; font-size: 18px; cursor: pointer; color: #666; padding: 0; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center;">×</button>
        </div>

        <div style="margin-bottom: 20px;">
            <h4 style="margin: 0 0 12px 0; font-size: 14px; font-weight: bold; color: #333;">Node Types (Colors)</h4>
            <div style="margin-bottom: 8px;">
                <div style="background-color: #FFFF00; width: 20px; height: 20px; display: inline-block; margin-right: 10px; border: 1px solid #999; border-radius: 3px;"></div>
                <span style="vertical-align: top; line-height: 20px;">Groups</span>
            </div>
            <div style="margin-bottom: 8px;">
                <div style="background-color: #00cc66; width: 20px; height: 20px; display: inline-block; margin-right: 10px; border: 1px solid #999; border-radius: 3px;"></div>
                <span style="vertical-align: top; line-height: 20px;">Tags</span>
            </div>
            <div style="margin-bottom: 8px;">
                <div style="background-color: #ff6666; width: 20px; height: 20px; display: inline-block; margin-right: 10px; border: 1px solid #999; border-radius: 3px;"></div>
                <span style="vertical-align: top; line-height: 20px;">Hosts</span>
            </div>
        </div>

        <div style="margin-bottom: 20px;">
            <h4 style="margin: 0 0 12px 0; font-size: 14px; font-weight: bold; color: #333;">Rule Types (Shapes)</h4>
            <div style="margin-bottom: 8px;">
                <span style="font-size: 18px; margin-right: 10px; color: #333;">●</span>
                <span style="vertical-align: top; line-height: 18px;">ACL rules only</span>
            </div>
            <div style="margin-bottom: 8px;">
                <span style="font-size: 18px; margin-right: 10px; color: #333;">▲</span>
                <span style="vertical-align: top; line-height: 18px;">Grant rules only</span>
            </div>
            <div style="margin-bottom: 8px;">
                <span style="font-size: 18px; margin-right: 10px; color: #333;">⬢</span>
                <span style="vertical-align: top; line-height: 18px;">Both ACL and Grant rules</span>
            </div>
        </div>

        <div style="border-top: 1px solid #ddd; padding-top: 15px; font-size: 11px; color: #666;">
            <p style="margin: 0 0 8px 0;"><strong>Tip:</strong> Hover over nodes for detailed information including rule references with line numbers.</p>
            <p style="margin: 0;"><strong>Search:</strong> Use the search box to filter nodes by any metadata.</p>
        </div>
    </div>
</div>

<script>
// Global legend state
let isLegendVisible = true; // Default to visible

// Global function for legend toggle
function toggleLegend() {
    const legendPanel = document.getElementById('legend-panel');
    isLegendVisible = !isLegendVisible;
    if (isLegendVisible) {
        legendPanel.style.transform = 'translateX(0)';
    } else {
        legendPanel.style.transform = 'translateX(100%)';
    }
}

document.addEventListener('DOMContentLoaded', function() {
    const legendPanel = document.getElementById('legend-panel');
    const closeButton = document.getElementById('legend-close');

    // Close button click
    closeButton.addEventListener('click', function() {
        toggleLegend();
    });

    // Initialize - legend is visible by default
    legendPanel.style.transform = 'translateX(0)';
});
</script>
