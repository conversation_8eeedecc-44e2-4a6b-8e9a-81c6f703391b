"""
Live topology builder for Tailscale network visualization.

This module provides functionality to build network graphs from live Tailscale
API data, showing actual host-to-tag relationships and device status. It creates
a different view from the policy-based topology by showing what devices are
actually connected and how they're tagged in the live network.

Classes:
    LiveTopologyBuilder: Main class for building live topology graphs
    LiveNetworkGraph: Extended network graph for live topology data

Features:
    - Live device discovery via Tailscale API
    - Host-to-tag relationship mapping
    - Device status visualization (online/offline)
    - Integration with existing visualization pipeline
"""

import logging
from typing import Dict, List, Set, Tuple, Optional
from collections import defaultdict

from network_graph import NetworkGraph
from services.tailscale_api import TailscaleAPIClient, TailscaleDevice, TailscaleAPIError
from config import NODE_COLORS, is_tailscale_api_configured


class LiveNetworkGraph(NetworkGraph):
    """
    Extended network graph for live topology visualization.
    
    Extends the base NetworkGraph class to support live topology data
    from the Tailscale API, including device status and real-time
    tag assignments.
    """
    
    def __init__(self):
        """Initialize the live network graph with empty data structures."""
        # Initialize with empty data since we'll populate from API
        super().__init__(hosts={}, groups={}, rule_line_numbers={'acls': [], 'grants': []})
        self.live_devices: List[TailscaleDevice] = []
        self.device_status: Dict[str, bool] = {}  # device_id -> online status
        
    def build_live_graph(self, devices: List[TailscaleDevice]) -> None:
        """
        Build the network graph from live Tailscale device data.
        
        Creates a graph showing actual host-to-tag relationships based on
        live device information from the Tailscale API. Each device with
        tags creates connections from the device node to each assigned tag.
        
        Args:
            devices: List of TailscaleDevice objects from the API
        """
        logging.debug(f"Building live topology graph from {len(devices)} devices")
        
        # Clear existing data
        self.nodes.clear()
        self.edges.clear()
        self.node_metadata.clear()
        self.edge_metadata.clear()
        
        # Store device data
        self.live_devices = devices
        
        # Track all unique tags across devices
        all_tags: Set[str] = set()
        device_tag_map: Dict[str, List[str]] = {}
        
        # First pass: collect all tags and build device-tag mapping
        for device in devices:
            device_id = device.hostname or device.name or device.id
            self.device_status[device_id] = device.online
            
            if device.tags:
                device_tag_map[device_id] = device.tags
                all_tags.update(device.tags)
                logging.debug(f"Device {device_id} has tags: {device.tags}")
        
        # Create tag nodes
        for tag in all_tags:
            tag_color = NODE_COLORS["tag"]
            tag_tooltip = self._create_tag_tooltip(tag, devices)
            self.add_node(tag, tag_color, tag_tooltip, "triangle")
            
            # Store tag metadata for search
            self.node_metadata[tag] = {
                "type": "tag",
                "name": tag,
                "rule_source": "Live API",
                "devices": [d.hostname or d.name for d in devices if tag in d.tags]
            }
        
        # Create device nodes and edges
        for device in devices:
            device_id = device.hostname or device.name or device.id
            device_color = self._get_device_color(device)
            device_tooltip = self._create_device_tooltip(device)
            device_shape = "dot"
            
            # Add device node
            self.add_node(device_id, device_color, device_tooltip, device_shape)
            
            # Store device metadata for search
            self.node_metadata[device_id] = {
                "type": "host",
                "name": device_id,
                "hostname": device.hostname,
                "addresses": device.addresses,
                "tags": device.tags,
                "online": device.online,
                "os": device.os,
                "client_version": device.client_version,
                "rule_source": "Live API"
            }
            
            # Create edges from device to each of its tags
            for tag in device.tags:
                self.add_edge(device_id, tag)
                
                # Store edge metadata
                edge_key = (device_id, tag)
                self.edge_metadata[edge_key] = {
                    "type": "host_to_tag",
                    "source": device_id,
                    "destination": tag,
                    "rule_source": "Live API",
                    "device_online": device.online
                }
                
                logging.debug(f"Created edge: {device_id} -> {tag}")
        
        logging.info(f"Live topology graph built: {len(self.nodes)} nodes, {len(self.edges)} edges")
    
    def _get_device_color(self, device: TailscaleDevice) -> str:
        """
        Get the appropriate color for a device node based on its status.
        
        Args:
            device: TailscaleDevice object
            
        Returns:
            Hex color code for the device
        """
        base_color = NODE_COLORS["host"]
        
        if not device.online:
            # Make offline devices more muted (darker)
            return "#cc4444"  # Darker red for offline devices
        
        return base_color
    
    def _create_device_tooltip(self, device: TailscaleDevice) -> str:
        """
        Create a comprehensive tooltip for a device node.
        
        Args:
            device: TailscaleDevice object
            
        Returns:
            HTML tooltip string with device information
        """
        status_emoji = "🟢" if device.online else "🔴"
        status_text = "Online" if device.online else "Offline"
        
        tooltip_parts = [
            f"<strong>{device.hostname or device.name}</strong>",
            f"{status_emoji} Status: {status_text}"
        ]
        
        if device.addresses:
            tooltip_parts.append(f"📍 Addresses: {', '.join(device.addresses[:3])}")
            if len(device.addresses) > 3:
                tooltip_parts.append(f"    ... and {len(device.addresses) - 3} more")
        
        if device.tags:
            tooltip_parts.append(f"🏷️ Tags: {', '.join(device.tags)}")
        
        if device.os:
            tooltip_parts.append(f"💻 OS: {device.os}")
        
        if device.client_version:
            tooltip_parts.append(f"📦 Client: {device.client_version}")
        
        if device.last_seen:
            tooltip_parts.append(f"👁️ Last Seen: {device.last_seen}")
        
        tooltip_parts.append("<br><em>Source: Live Tailscale API</em>")
        
        return "<br>".join(tooltip_parts)
    
    def _create_tag_tooltip(self, tag: str, devices: List[TailscaleDevice]) -> str:
        """
        Create a tooltip for a tag node showing which devices have this tag.
        
        Args:
            tag: Tag name
            devices: List of all devices
            
        Returns:
            HTML tooltip string with tag information
        """
        tagged_devices = [d for d in devices if tag in d.tags]
        online_count = sum(1 for d in tagged_devices if d.online)
        offline_count = len(tagged_devices) - online_count
        
        tooltip_parts = [
            f"<strong>{tag}</strong>",
            f"📊 Devices: {len(tagged_devices)} total"
        ]
        
        if online_count > 0:
            tooltip_parts.append(f"🟢 Online: {online_count}")
        
        if offline_count > 0:
            tooltip_parts.append(f"🔴 Offline: {offline_count}")
        
        if tagged_devices:
            device_names = [d.hostname or d.name for d in tagged_devices[:5]]
            tooltip_parts.append(f"🖥️ Devices: {', '.join(device_names)}")
            if len(tagged_devices) > 5:
                tooltip_parts.append(f"    ... and {len(tagged_devices) - 5} more")
        
        tooltip_parts.append("<br><em>Source: Live Tailscale API</em>")
        
        return "<br>".join(tooltip_parts)


class LiveTopologyBuilder:
    """
    Builder class for creating live topology graphs from Tailscale API data.
    
    Coordinates the fetching of live device data from the Tailscale API
    and the construction of network graphs showing actual host-to-tag
    relationships in the live network.
    """
    
    def __init__(self, api_client: Optional[TailscaleAPIClient] = None):
        """
        Initialize the live topology builder.
        
        Args:
            api_client: Optional TailscaleAPIClient instance. If not provided,
                       a new client will be created.
        """
        self.api_client = api_client or TailscaleAPIClient()
        
    def is_available(self) -> bool:
        """
        Check if live topology features are available.
        
        Returns:
            True if API is configured and credentials are valid, False otherwise
        """
        if not is_tailscale_api_configured():
            logging.debug("Tailscale API not configured")
            return False
        
        try:
            return self.api_client.validate_credentials()
        except TailscaleAPIError as e:
            logging.error(f"Tailscale API validation failed: {e}")
            return False
    
    def build_live_topology(self) -> Optional[LiveNetworkGraph]:
        """
        Build a live topology graph from current Tailscale API data.
        
        Returns:
            LiveNetworkGraph instance with current device data, or None if unavailable
            
        Raises:
            TailscaleAPIError: If API requests fail
        """
        if not self.is_available():
            logging.warning("Live topology not available - API not configured or invalid")
            return None
        
        try:
            logging.info("Fetching live device data from Tailscale API")
            devices = self.api_client.get_devices()
            
            if not devices:
                logging.warning("No devices found in Tailscale API response")
                return None
            
            # Build the live topology graph
            live_graph = LiveNetworkGraph()
            live_graph.build_live_graph(devices)
            
            logging.info(f"Live topology built successfully with {len(devices)} devices")
            return live_graph
            
        except TailscaleAPIError as e:
            logging.error(f"Failed to build live topology: {e}")
            raise
    
    def get_api_status(self) -> Dict[str, any]:
        """
        Get the current status of the Tailscale API integration.
        
        Returns:
            Dictionary with API configuration and status information
        """
        status = {
            "configured": is_tailscale_api_configured(),
            "credentials_valid": False,
            "error": None
        }
        
        if status["configured"]:
            try:
                status["credentials_valid"] = self.api_client.validate_credentials()
            except TailscaleAPIError as e:
                status["error"] = str(e)
        
        return status
