#!/usr/bin/env python3
"""
Test the search functionality to ensure it works correctly for all metadata types.

This test suite verifies the search functionality using mock data instead of
external policy files, ensuring tests are isolated and reliable.

FEATURES TESTED:
- Enhanced search logic to treat certain keywords as metadata type searches
- When searching for "via", show all nodes that HAVE via routing (regardless of route values)
- When searching for "protocol"/"protocols", show all nodes that HAVE protocols
- When searching for "posture", show all nodes that HAVE posture checks
- When searching for "app"/"apps", show all nodes that HAVE applications
- Support for searching within the actual values (e.g., "api-gateway", "tcp:443")

This makes the search more intuitive and user-friendly.
"""

import unittest
import sys
import os

# Add the parent directory to the path so we can import modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from network_graph import NetworkGraph
from tests.test_utils import TestGraphBuilder, SearchTestHelper
from tests.fixtures.mock_policy_data import MockPolicyData, MockLineNumbers


class TestSearchFunctionality(unittest.TestCase):
    """Test cases for search functionality using mock data."""

    def setUp(self):
        """Set up test fixtures with mock data."""
        # Create comprehensive test graph with mock data
        self.graph = TestGraphBuilder.create_search_test_graph()

        # Get search metadata
        self.search_metadata = self.graph.get_search_metadata()

        # Also create comprehensive graph for additional tests
        self.comprehensive_graph = TestGraphBuilder.create_comprehensive_graph()
        self.comprehensive_metadata = self.comprehensive_graph.get_search_metadata()

    def test_via_routing_extraction(self):
        """Test that via routing information is correctly extracted."""
        # Test with comprehensive metadata that has more via routing examples
        nodes_with_via = []
        for node_id, metadata in self.comprehensive_metadata['nodes'].items():
            if metadata.get('via') and len(metadata['via']) > 0:
                nodes_with_via.append((node_id, metadata['via']))

        # We should have nodes with via routing
        self.assertGreater(len(nodes_with_via), 0, "No nodes with via routing found")

        # Check specific expected via routes from mock data
        via_routes_found = []
        for node_id, via_routes in nodes_with_via:
            via_routes_found.extend(via_routes)

        expected_via_routes = ['tag:exit-node-nyc', 'api-gateway', 'internal-tool']
        for expected_route in expected_via_routes:
            self.assertIn(expected_route, via_routes_found,
                         f"Expected via route '{expected_route}' not found")

        print(f"✓ Found {len(nodes_with_via)} nodes with via routing")
        for node_id, via_routes in nodes_with_via:
            print(f"  - {node_id}: {via_routes}")

    def test_protocol_extraction(self):
        """Test that protocol information is correctly extracted."""
        # Use comprehensive metadata for more protocol examples
        nodes_with_protocols = []
        for node_id, metadata in self.comprehensive_metadata['nodes'].items():
            protocols = metadata.get('protocols', [])
            # Filter out empty and wildcard protocols
            specific_protocols = [p for p in protocols if p and p != "*"]
            if specific_protocols:
                nodes_with_protocols.append((node_id, specific_protocols))

        self.assertGreater(len(nodes_with_protocols), 0, "No nodes with specific protocols found")

        # Check for expected protocol patterns from mock data
        all_protocols = []
        for node_id, protocols in nodes_with_protocols:
            all_protocols.extend(protocols)

        # Should have TCP protocols
        tcp_protocols = [p for p in all_protocols if 'tcp:' in p.lower()]
        self.assertGreater(len(tcp_protocols), 0, "No TCP protocols found")

        # Should have UDP protocols from mock data
        udp_protocols = [p for p in all_protocols if 'udp:' in p.lower()]
        self.assertGreater(len(udp_protocols), 0, "No UDP protocols found")

        print(f"✓ Found {len(nodes_with_protocols)} nodes with specific protocols")
        print(f"  - Total unique protocols: {len(set(all_protocols))}")
        print(f"  - TCP protocols: {len(tcp_protocols)}")
        print(f"  - UDP protocols: {len(udp_protocols)}")

    def test_posture_extraction(self):
        """Test that posture check information is correctly extracted."""
        # Use comprehensive metadata for more posture examples
        nodes_with_posture = []
        for node_id, metadata in self.comprehensive_metadata['nodes'].items():
            if metadata.get('posture') and len(metadata['posture']) > 0:
                nodes_with_posture.append((node_id, metadata['posture']))

        self.assertGreater(len(nodes_with_posture), 0, "No nodes with posture checks found")

        # Check for expected posture checks from mock data
        all_posture_checks = []
        for node_id, posture_checks in nodes_with_posture:
            all_posture_checks.extend(posture_checks)

        expected_posture_checks = ['posture:complianceDevice', 'posture:latestMac', 'posture:trustedDevice']
        for expected_check in expected_posture_checks:
            self.assertIn(expected_check, all_posture_checks,
                         f"Expected posture check '{expected_check}' not found")

        print(f"✓ Found {len(nodes_with_posture)} nodes with posture checks")
        for node_id, posture_checks in nodes_with_posture:
            print(f"  - {node_id}: {posture_checks}")

    def test_application_extraction(self):
        """Test that application information is correctly extracted."""
        # Use comprehensive metadata for more app examples
        nodes_with_apps = []
        for node_id, metadata in self.comprehensive_metadata['nodes'].items():
            if metadata.get('apps') and len(metadata['apps']) > 0:
                nodes_with_apps.append((node_id, metadata['apps']))

        self.assertGreater(len(nodes_with_apps), 0, "No nodes with applications found")

        # Check for expected applications from mock data
        all_apps = []
        for node_id, apps in nodes_with_apps:
            all_apps.extend(apps)

        expected_apps = ['example.com/webapp-connector', 'example.com/service-mesh', 'example.com/deployment-pipeline']
        for expected_app in expected_apps:
            self.assertIn(expected_app, all_apps,
                         f"Expected application '{expected_app}' not found")

        print(f"✓ Found {len(nodes_with_apps)} nodes with applications")
        for node_id, apps in nodes_with_apps:
            print(f"  - {node_id}: {apps}")

    def test_search_logic_simulation(self):
        """Simulate the search logic to ensure it works correctly."""

        # Test various search terms with comprehensive metadata
        test_cases = [
            ('via', 1),  # Should find nodes with via routing
            ('api-gateway', 1),  # Should find nodes with api-gateway in via routes
            ('tcp', 1),  # Should find nodes with TCP protocols
            ('posture', 1),  # Should find nodes with posture checks
            ('app', 1),  # Should find nodes with applications
            ('compliance', 1),  # Should find nodes with complianceDevice posture
        ]

        for search_term, expected_min_results in test_cases:
            results = SearchTestHelper.simulate_search(self.comprehensive_metadata, search_term)
            self.assertGreaterEqual(len(results), expected_min_results,
                                  f"Search for '{search_term}' returned {len(results)} results, expected at least {expected_min_results}")
            print(f"✓ Search for '{search_term}': {len(results)} results")

            # Show first few results
            for i, result in enumerate(results[:3]):
                print(f"  - {result['id']} (matched: {', '.join(result['match_details'])})")

    def test_specific_search_scenarios(self):
        """Test specific search scenarios with dedicated test data."""
        # Use the search-specific test graph
        search_metadata = self.search_metadata

        # Test protocol search
        protocol_results = SearchTestHelper.simulate_search(search_metadata, 'tcp')
        self.assertGreater(len(protocol_results), 0, "Should find nodes with TCP protocols")

        # Test via search
        via_results = SearchTestHelper.simulate_search(search_metadata, 'via')
        self.assertGreater(len(via_results), 0, "Should find nodes with via routing")

        # Test posture search
        posture_results = SearchTestHelper.simulate_search(search_metadata, 'posture')
        self.assertGreater(len(posture_results), 0, "Should find nodes with posture checks")

        # Test app search
        app_results = SearchTestHelper.simulate_search(search_metadata, 'app')
        self.assertGreater(len(app_results), 0, "Should find nodes with applications")

        print(f"✓ Specific search scenarios:")
        print(f"  - TCP protocol search: {len(protocol_results)} results")
        print(f"  - Via routing search: {len(via_results)} results")
        print(f"  - Posture check search: {len(posture_results)} results")
        print(f"  - Application search: {len(app_results)} results")


if __name__ == '__main__':
    unittest.main(verbosity=2)
