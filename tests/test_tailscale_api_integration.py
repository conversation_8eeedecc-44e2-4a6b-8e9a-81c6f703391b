"""
Test suite for Tailscale API integration functionality.

This module contains comprehensive tests for the Tailscale API client,
live topology builder, and enhanced renderer functionality. Tests cover
both successful operations and error handling scenarios.

Test Classes:
    TestTailscaleAPIClient: Tests for the API client functionality
    TestLiveTopologyBuilder: Tests for live topology graph construction
    TestEnhancedRenderer: Tests for dual-mode rendering capabilities

Features Tested:
    - API authentication and validation
    - Device data fetching and parsing
    - Live topology graph construction
    - Enhanced renderer with view toggle
    - Error handling and fallback behavior
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
import json
import tempfile
import os

from services.tailscale_api import TailscaleAPIClient, TailscaleDevice, TailscaleAPIError
from live_topology import LiveTopologyBuilder, LiveNetworkGraph
from enhanced_renderer import Enhanced<PERSON>enderer
from network_graph import NetworkGraph


class TestTailscaleAPIClient(unittest.TestCase):
    """Test cases for TailscaleAPIClient functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.mock_config = {
            "api_key": "test-api-key",
            "oauth_token": None,
            "tailnet": "test-tailnet",
            "base_url": "https://api.tailscale.com/api/v2"
        }
        
    @patch('services.tailscale_api.TAILSCALE_API_CONFIG')
    @patch('services.tailscale_api.get_tailscale_auth_header')
    def test_client_initialization(self, mock_auth_header, mock_config):
        """Test TailscaleAPIClient initialization."""
        mock_config.__getitem__.side_effect = self.mock_config.__getitem__
        mock_auth_header.return_value = "Bearer test-api-key"
        
        client = TailscaleAPIClient()
        
        self.assertEqual(client.base_url, "https://api.tailscale.com/api/v2")
        self.assertEqual(client.tailnet, "test-tailnet")
        self.assertEqual(client.auth_header, "Bearer test-api-key")
    
    @patch('services.tailscale_api.is_tailscale_api_configured')
    def test_is_configured(self, mock_is_configured):
        """Test is_configured method."""
        mock_is_configured.return_value = True
        
        client = TailscaleAPIClient()
        self.assertTrue(client.is_configured())
        
        mock_is_configured.return_value = False
        self.assertFalse(client.is_configured())
    
    @patch('services.tailscale_api.requests.Session')
    @patch('services.tailscale_api.is_tailscale_api_configured')
    def test_validate_credentials_success(self, mock_is_configured, mock_session_class):
        """Test successful credential validation."""
        mock_is_configured.return_value = True
        mock_session = Mock()
        mock_session_class.return_value = mock_session
        
        mock_response = Mock()
        mock_response.status_code = 200
        mock_session.request.return_value = mock_response
        
        client = TailscaleAPIClient()
        result = client.validate_credentials()
        
        self.assertTrue(result)
    
    @patch('services.tailscale_api.requests.Session')
    @patch('services.tailscale_api.is_tailscale_api_configured')
    def test_validate_credentials_unauthorized(self, mock_is_configured, mock_session_class):
        """Test credential validation with unauthorized response."""
        mock_is_configured.return_value = True
        mock_session = Mock()
        mock_session_class.return_value = mock_session
        
        mock_response = Mock()
        mock_response.status_code = 401
        mock_session.request.return_value = mock_response
        
        client = TailscaleAPIClient()
        result = client.validate_credentials()
        
        self.assertFalse(result)
    
    @patch('services.tailscale_api.requests.Session')
    @patch('services.tailscale_api.is_tailscale_api_configured')
    def test_get_devices_success(self, mock_is_configured, mock_session_class):
        """Test successful device fetching."""
        mock_is_configured.return_value = True
        mock_session = Mock()
        mock_session_class.return_value = mock_session
        
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "devices": [
                {
                    "id": "device1",
                    "hostname": "test-host-1",
                    "name": "Test Host 1",
                    "tags": ["tag:server", "tag:production"],
                    "addresses": ["**********"],
                    "online": True,
                    "os": "linux",
                    "clientVersion": "1.32.0"
                },
                {
                    "id": "device2",
                    "hostname": "test-host-2",
                    "name": "Test Host 2",
                    "tags": ["tag:client"],
                    "addresses": ["**********"],
                    "online": False,
                    "os": "windows"
                }
            ]
        }
        mock_session.request.return_value = mock_response
        
        client = TailscaleAPIClient()
        devices = client.get_devices()
        
        self.assertEqual(len(devices), 2)
        self.assertIsInstance(devices[0], TailscaleDevice)
        self.assertEqual(devices[0].hostname, "test-host-1")
        self.assertEqual(devices[0].tags, ["tag:server", "tag:production"])
        self.assertTrue(devices[0].online)
        self.assertFalse(devices[1].online)


class TestLiveTopologyBuilder(unittest.TestCase):
    """Test cases for LiveTopologyBuilder functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.mock_devices = [
            TailscaleDevice(
                id="device1",
                hostname="test-host-1",
                name="Test Host 1",
                tags=["tag:server", "tag:production"],
                addresses=["**********"],
                online=True,
                os="linux"
            ),
            TailscaleDevice(
                id="device2",
                hostname="test-host-2",
                name="Test Host 2",
                tags=["tag:client"],
                addresses=["**********"],
                online=False,
                os="windows"
            )
        ]
    
    @patch('live_topology.is_tailscale_api_configured')
    def test_is_available_not_configured(self, mock_is_configured):
        """Test is_available when API is not configured."""
        mock_is_configured.return_value = False
        
        builder = LiveTopologyBuilder()
        self.assertFalse(builder.is_available())
    
    @patch('live_topology.is_tailscale_api_configured')
    def test_is_available_configured_but_invalid(self, mock_is_configured):
        """Test is_available when API is configured but credentials are invalid."""
        mock_is_configured.return_value = True
        
        mock_api_client = Mock()
        mock_api_client.validate_credentials.return_value = False
        
        builder = LiveTopologyBuilder(mock_api_client)
        self.assertFalse(builder.is_available())
    
    def test_build_live_topology_success(self):
        """Test successful live topology building."""
        mock_api_client = Mock()
        mock_api_client.validate_credentials.return_value = True
        mock_api_client.get_devices.return_value = self.mock_devices
        
        builder = LiveTopologyBuilder(mock_api_client)
        
        with patch.object(builder, 'is_available', return_value=True):
            live_graph = builder.build_live_topology()
        
        self.assertIsInstance(live_graph, LiveNetworkGraph)
        self.assertEqual(len(live_graph.live_devices), 2)
    
    def test_build_live_topology_not_available(self):
        """Test live topology building when not available."""
        mock_api_client = Mock()
        
        builder = LiveTopologyBuilder(mock_api_client)
        
        with patch.object(builder, 'is_available', return_value=False):
            live_graph = builder.build_live_topology()
        
        self.assertIsNone(live_graph)


class TestLiveNetworkGraph(unittest.TestCase):
    """Test cases for LiveNetworkGraph functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.mock_devices = [
            TailscaleDevice(
                id="device1",
                hostname="test-host-1",
                name="Test Host 1",
                tags=["tag:server", "tag:production"],
                addresses=["**********"],
                online=True,
                os="linux"
            ),
            TailscaleDevice(
                id="device2",
                hostname="test-host-2",
                name="Test Host 2",
                tags=["tag:client"],
                addresses=["**********"],
                online=False,
                os="windows"
            )
        ]
    
    def test_build_live_graph(self):
        """Test building live graph from device data."""
        live_graph = LiveNetworkGraph()
        live_graph.build_live_graph(self.mock_devices)
        
        # Check that nodes were created for devices and tags
        node_ids = [node[0] for node in live_graph.nodes]
        
        # Should have device nodes
        self.assertIn("test-host-1", node_ids)
        self.assertIn("test-host-2", node_ids)
        
        # Should have tag nodes
        self.assertIn("tag:server", node_ids)
        self.assertIn("tag:production", node_ids)
        self.assertIn("tag:client", node_ids)
        
        # Check edges (device to tag connections)
        edge_pairs = [(edge[0], edge[1]) for edge in live_graph.edges]
        self.assertIn(("test-host-1", "tag:server"), edge_pairs)
        self.assertIn(("test-host-1", "tag:production"), edge_pairs)
        self.assertIn(("test-host-2", "tag:client"), edge_pairs)
    
    def test_device_color_online_offline(self):
        """Test device color assignment based on online status."""
        live_graph = LiveNetworkGraph()
        
        online_device = self.mock_devices[0]  # online=True
        offline_device = self.mock_devices[1]  # online=False
        
        online_color = live_graph._get_device_color(online_device)
        offline_color = live_graph._get_device_color(offline_device)
        
        # Offline devices should have a different (darker) color
        self.assertNotEqual(online_color, offline_color)
        self.assertEqual(offline_color, "#cc4444")


class TestEnhancedRenderer(unittest.TestCase):
    """Test cases for EnhancedRenderer functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create a basic network graph for testing
        self.network_graph = NetworkGraph(
            hosts={"host1": ["**********"]},
            groups={"group:servers": ["host1"]},
            rule_line_numbers={"acls": [], "grants": []}
        )
        
        self.temp_file = None
    
    def tearDown(self):
        """Clean up test fixtures."""
        if self.temp_file and os.path.exists(self.temp_file):
            os.unlink(self.temp_file)
    
    @patch('enhanced_renderer.is_tailscale_api_configured')
    def test_enhanced_renderer_no_api(self, mock_is_configured):
        """Test enhanced renderer when API is not configured."""
        mock_is_configured.return_value = False
        
        renderer = EnhancedRenderer(self.network_graph)
        
        self.assertFalse(renderer.live_available)
        self.assertIsNone(renderer.live_graph)
    
    @patch('enhanced_renderer.is_tailscale_api_configured')
    def test_enhanced_renderer_with_api(self, mock_is_configured):
        """Test enhanced renderer when API is configured."""
        mock_is_configured.return_value = True
        
        mock_live_builder = Mock()
        mock_live_builder.is_available.return_value = True
        
        renderer = EnhancedRenderer(self.network_graph, mock_live_builder)
        
        self.assertTrue(renderer.live_available)
    
    @patch('enhanced_renderer.is_tailscale_api_configured')
    def test_render_to_html_with_live_data(self, mock_is_configured):
        """Test HTML rendering with live topology data."""
        mock_is_configured.return_value = True
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False) as f:
            self.temp_file = f.name
            f.write('<html><head></head><body></body></html>')
        
        mock_live_builder = Mock()
        mock_live_builder.is_available.return_value = True
        
        mock_live_graph = Mock()
        mock_live_graph.nodes = [
            ("test-host", "#ff0000", "Test tooltip", "dot"),
            ("tag:test", "#00ff00", "Tag tooltip", "triangle")
        ]
        mock_live_graph.edges = [("test-host", "tag:test")]
        mock_live_builder.build_live_topology.return_value = mock_live_graph
        
        renderer = EnhancedRenderer(self.network_graph, mock_live_builder)

        # Mock the parent render method to avoid actual rendering
        with patch.object(renderer.__class__.__bases__[0], 'render_to_html') as mock_render:
            # Set the output file path that the enhanced renderer expects
            renderer.output_file = self.temp_file
            renderer.render_to_html(self.temp_file)
        
        # Check that live data was added to the file
        with open(self.temp_file, 'r') as f:
            content = f.read()
        
        self.assertIn('liveTopologyData', content)
        self.assertIn('test-host', content)
        self.assertIn('tag:test', content)


if __name__ == '__main__':
    unittest.main()
